# 五子棋大师 (Gomoku Master)

一个功能完整的五子棋游戏，支持多种游戏模式和界面。

## 🎯 功能特性

### 游戏模式
- **人机对战 (PvE)**: 与AI对手进行游戏，支持多种难度级别
- **在线对战 (PvP)**: 与其他玩家实时对战
- **本地对战**: 双人在同一设备上游戏

### 界面支持
- **命令行界面 (CLI)**: 纯文本界面，适合终端使用
- **图形界面 (GUI)**: 基于Tkinter的桌面应用
- **网页界面 (Web)**: 基于Flask的现代Web应用

### AI特性
- **多难度级别**: 简单、中等、困难
- **智能算法**: 使用Minimax算法和启发式评估
- **实时响应**: 快速计算最佳落子位置

### 网页版特性
- **实时对战**: 基于WebSocket的实时通信
- **房间系统**: 创建和加入游戏房间
- **响应式设计**: 支持桌面和移动设备
- **现代UI**: 美观的用户界面和动画效果

## 📁 项目结构

```
五子棋/
├── core/                   # 核心游戏逻辑模块
│   ├── __init__.py        # 模块初始化
│   ├── game_engine.py     # 游戏引擎
│   ├── room_manager.py    # 房间管理
│   └── player_manager.py  # 玩家管理
├── templates/             # HTML模板
│   ├── base.html         # 基础模板
│   ├── index.html        # 首页
│   └── game.html         # 游戏页面
├── static/               # 静态资源
│   ├── css/
│   │   └── style.css     # 样式文件
│   └── js/
│       └── app.js        # 客户端脚本
├── app.py                # Flask Web应用
├── main.py               # 主程序入口
├── main_gui.py           # GUI程序入口
├── game.py               # 游戏逻辑
├── board.py              # 棋盘逻辑
├── player.py             # 玩家类
├── ai.py                 # AI逻辑
├── gui.py                # GUI界面
├── utils.py              # 工具函数
├── uv.lock              # uv依赖锁定文件
└── README.md            # 项目说明
```

## 🚀 快速开始

### 环境要求
- Python 3.7+
- 操作系统: Windows, macOS, Linux

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd 五子棋

# 安装依赖
uv sync
```

### 运行游戏

#### 1. 网页版 (推荐)
```bash
# 启动Web服务器
python main.py --mode web

# 自定义主机和端口
python main.py --mode web --host 0.0.0.0 --port 8080

# 启用调试模式
python main.py --mode web --debug
```

然后在浏览器中访问 `http://localhost:5000`

#### 2. 图形界面
```bash
# 启动GUI版本
python main.py --mode gui
```

#### 3. 命令行界面
```bash
# 启动CLI版本
python main.py --mode cli

# 人机对战
python main.py --mode cli --player1 human --player2 ai --ai-level hard

# 双人对战
python main.py --mode cli --player1 human --player2 human
```

## 🎮 游戏说明

### 基本规则
1. 两名玩家轮流在15×15的棋盘上落子
2. 黑子先手，白子后手
3. 率先在横、竖、斜任意方向连成5子者获胜
4. 棋盘填满且无人获胜则为平局

### 网页版使用指南

#### 创建游戏
1. 访问首页
2. 输入玩家昵称
3. 选择游戏模式（在线对战或人机对战）
4. 点击"创建房间"或"开始游戏"

#### 加入游戏
1. 获取房间ID
2. 在"加入房间"输入框中输入房间ID
3. 点击加入按钮

#### 游戏操作
- **落子**: 点击棋盘空白位置
- **悔棋**: 点击"悔棋"按钮（需对方同意）
- **重开**: 点击"重开"按钮
- **离开**: 点击"离开"按钮退出游戏

## 🔧 配置选项

### 命令行参数
- `--mode`: 界面模式 (cli/gui/web)
- `--player1`: 玩家1类型 (human/ai)
- `--player2`: 玩家2类型 (human/ai)
- `--ai-level`: AI难度 (easy/medium/hard)
- `--host`: Web服务器主机地址
- `--port`: Web服务器端口
- `--debug`: 启用调试模式

### AI难度说明
- **简单**: 随机落子，适合初学者
- **中等**: 基础策略，有一定挑战性
- **困难**: 高级算法，具有较强实力

## 🛠️ 开发说明

### 代码结构
- **core/**: 核心游戏逻辑，可复用的组件
- **templates/**: Jinja2模板文件
- **static/**: CSS、JavaScript等静态资源
- **app.py**: Flask应用主文件
- **main.py**: 统一入口点

### 扩展功能
1. **添加新的AI算法**: 修改 `ai.py` 文件
2. **自定义UI主题**: 修改 `static/css/style.css`
3. **添加新的游戏模式**: 扩展 `core/game_engine.py`
4. **集成数据库**: 添加用户系统和游戏记录

## 🐛 故障排除

### 常见问题

1. **GUI界面无法启动**
   ```
   错误: 无法启动GUI界面，tkinter模块未找到
   ```
   解决方案: 使用Web模式或CLI模式
   ```bash
   python main.py --mode web
   ```

2. **Web界面无法启动**
   ```
   错误: 无法启动Web界面，Flask模块未找到
   ```
   解决方案: 安装依赖包
   ```bash
   uv sync
   ```

3. **端口被占用**
   ```
   Address already in use
   ```
   解决方案: 使用不同端口
   ```bash
   python main.py --mode web --port 8080
   ```

## 📝 更新日志

### v2.0.0 (当前版本)
- ✨ 新增网页版界面
- ✨ 支持在线实时对战
- ✨ 添加房间系统
- ✨ 重构代码结构
- 🎨 改进UI设计
- 🐛 修复已知问题

### v1.0.0
- ✨ 基础五子棋游戏
- ✨ CLI和GUI界面
- ✨ AI对战功能
- ✨ 多难度级别

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目采用MIT许可证。

---

**享受游戏！** 🎮