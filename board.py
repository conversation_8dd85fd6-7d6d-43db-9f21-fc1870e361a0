class Board:
    def __init__(self, size=15):
        self.size = size
        self.board = [[0 for _ in range(size)] for _ in range(size)]
        # 0: 空位, 1: 黑子, 2: 白子
    
    def is_valid_move(self, row, col):
        """检查落子位置是否有效"""
        return (0 <= row < self.size and 
                0 <= col < self.size and 
                self.board[row][col] == 0)
    
    def make_move(self, row, col, player):
        """落子"""
        if self.is_valid_move(row, col):
            self.board[row][col] = player
            return True
        return False
    
    def check_winner(self, row, col, player):
        """检查是否获胜（五子连珠）"""
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]  # 水平、垂直、对角线
        
        for dx, dy in directions:
            count = 1  # 包含当前落子
            
            # 正方向计数
            r, c = row + dx, col + dy
            while (0 <= r < self.size and 0 <= c < self.size and 
                   self.board[r][c] == player):
                count += 1
                r, c = r + dx, c + dy
            
            # 反方向计数
            r, c = row - dx, col - dy
            while (0 <= r < self.size and 0 <= c < self.size and 
                   self.board[r][c] == player):
                count += 1
                r, c = r - dx, c - dy
            
            if count >= 5:
                return True
        
        return False
    
    def is_full(self):
        """检查棋盘是否已满"""
        for row in self.board:
            if 0 in row:
                return False
        return True
    
    def display(self):
        """显示棋盘"""
        print("   ", end="")
        for i in range(self.size):
            print(f"{i:2}", end=" ")
        print()
        
        for i in range(self.size):
            print(f"{i:2} ", end="")
            for j in range(self.size):
                if self.board[i][j] == 0:
                    print(" .", end=" ")
                elif self.board[i][j] == 1:
                    print(" ●", end=" ")
                else:
                    print(" ○", end=" ")
            print()
        print()
    
    def get_empty_positions(self):
        """获取所有空位置"""
        positions = []
        for i in range(self.size):
            for j in range(self.size):
                if self.board[i][j] == 0:
                    positions.append((i, j))
        return positions