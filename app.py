#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五子棋游戏 - Web应用入口
作者: AI Assistant
描述: 基于Flask的五子棋Web应用
"""

from flask import Flask, render_template, request, jsonify, session
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
import uuid
import json
from datetime import datetime

# 导入游戏核心模块
from core.game_engine import GameEngine
from core.room_manager import RoomManager
from core.player_manager import PlayerManager

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# 全局管理器
room_manager = RoomManager()
player_manager = PlayerManager()

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/game')
def game():
    """游戏页面"""
    return render_template('game.html')

@app.route('/api/create_room', methods=['POST'])
def create_room():
    """创建游戏房间"""
    try:
        data = request.get_json()
        player_name = data.get('player_name', '匿名玩家')
        game_mode = data.get('game_mode', 'pvp')  # pvp, pve
        ai_difficulty = data.get('ai_difficulty', 'medium')
        
        # 创建房间
        room_id = room_manager.create_room(game_mode, ai_difficulty)
        
        # 创建玩家
        player_id = str(uuid.uuid4())
        player = player_manager.create_player(player_id, player_name, room_id)
        
        # 将玩家加入房间
        room_manager.add_player_to_room(room_id, player)
        
        return jsonify({
            'success': True,
            'room_id': room_id,
            'player_id': player_id,
            'player': player.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/join_room', methods=['POST'])
def join_room_api():
    """加入游戏房间"""
    try:
        data = request.get_json()
        room_id = data.get('room_id')
        player_name = data.get('player_name', '匿名玩家')
        
        # 检查房间是否存在
        if not room_manager.room_exists(room_id):
            return jsonify({'success': False, 'error': '房间不存在'})
        
        # 检查房间是否已满
        if room_manager.is_room_full(room_id):
            return jsonify({'success': False, 'error': '房间已满'})
        
        # 创建玩家
        player_id = str(uuid.uuid4())
        player = player_manager.create_player(player_id, player_name, room_id)
        
        # 将玩家加入房间
        room_manager.add_player_to_room(room_id, player)
        
        return jsonify({
            'success': True,
            'room_id': room_id,
            'player_id': player_id,
            'player': player.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/room/<room_id>/status')
def get_room_status(room_id):
    """获取房间状态"""
    try:
        room = room_manager.get_room(room_id)
        if not room:
            return jsonify({'success': False, 'error': '房间不存在'})
        
        return jsonify({
            'success': True,
            'room': room.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# WebSocket事件处理
@socketio.on('connect')
def on_connect():
    """Client connected"""
    print(f'Client connected: {request.sid}')

@socketio.on('disconnect')
def on_disconnect():
    """Client disconnected"""
    print(f'Client disconnected: {request.sid}')
    # Handle player leaving room logic
    player_manager.remove_player_by_session(request.sid)

@socketio.on('join_game_room')
def on_join_game_room(data):
    """加入游戏房间"""
    room_id = data.get('room_id')
    player_id = data.get('player_id')
    
    if room_id and player_id:
        join_room(room_id)
        player_manager.set_player_session(player_id, request.sid)
        
        # 通知房间内其他玩家
        emit('player_joined', {
            'player_id': player_id,
            'timestamp': datetime.now().isoformat()
        }, room=room_id, include_self=False)

@socketio.on('make_move')
def on_make_move(data):
    room_id = data.get('room_id')
    player_id = data.get('player_id')
    row = data.get('row')
    col = data.get('col')
    
    room = room_manager.get_room(room_id)
    if not room:
        emit('move_error', {'error': '房间不存在'})
        return
    
    game = room.game_engine
    if not game:
        emit('move_error', {'error': '游戏未开始'})
        return
    
    # 尝试进行移动
    result = game.make_move(player_id, row, col)
    if not result['success']:
        emit('move_error', {'error': result.get('error', '无效的移动')})
        return
    
    # 广播游戏更新
    game_state = game.get_state()
    # 添加最后一步移动的信息
    if 'last_move' in game_state and game_state['last_move']:
        game_state['last_move']['is_ai'] = False
    
    socketio.emit('game_update', game_state, room=room_id)
    
    # 检查游戏是否结束
    if game.is_game_over():
        winner = game.get_winner()
        game_over_data = {
            'game_over': True,
            'winner': winner.id if winner else None,
            'winner_name': winner.name if winner else None,
            'draw': game.is_draw()
        }
        socketio.emit('game_over', game_over_data, room=room_id)
        return
    
    # 如果是PVE模式且当前玩家是AI，自动进行AI移动
    if room.mode == 'pve' and game.get_current_player() and hasattr(game.get_current_player(), 'is_ai') and game.get_current_player().is_ai:
        # 获取AI移动
        ai_move = game.get_ai_move()
        if ai_move and 'row' in ai_move and 'col' in ai_move:
            # 执行AI移动
            ai_result = game.make_move(game.get_current_player().id, ai_move['row'], ai_move['col'])
            if ai_result['success']:
                # 广播AI移动更新
                ai_game_state = game.get_state()
                # 标记这是AI的移动
                if 'last_move' in ai_game_state and ai_game_state['last_move']:
                    ai_game_state['last_move']['is_ai'] = True
                
                socketio.emit('game_update', ai_game_state, room=room_id)
                
                # 检查游戏是否结束
                if game.is_game_over():
                    winner = game.get_winner()
                    game_over_data = {
                        'game_over': True,
                        'winner': winner.id if winner else None,
                        'winner_name': winner.name if winner else None,
                        'draw': game.is_draw()
                    }
                    socketio.emit('game_over', game_over_data, room=room_id)

@socketio.on('restart_game')
def on_restart_game(data):
    """重新开始游戏"""
    try:
        room_id = data.get('room_id')
        
        # 获取房间
        room = room_manager.get_room(room_id)
        if not room:
            emit('error', {'message': '房间不存在'})
            return
        
        # 重置游戏
        room.game_engine.reset_game()
        
        # 广播游戏重置
        game_state = room.game_engine.get_state()
        socketio.emit('game_reset', game_state, room=room_id)
        
    except Exception as e:
        emit('error', {'message': str(e)})

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)