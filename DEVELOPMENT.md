# 开发环境设置

本项目使用 [uv](https://github.com/astral-sh/uv) 进行Python环境和依赖管理。

## 环境要求

- Python 3.11+
- uv (Python包管理器)

## 快速开始

### 1. 安装uv（如果尚未安装）

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用Homebrew (macOS)
brew install uv

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 2. 设置项目环境

```bash
# 克隆或进入项目目录
cd /path/to/5子棋

# 同步依赖并创建虚拟环境
uv sync
```

### 3. 运行游戏

```bash
# 使用uv运行（推荐）
uv run python main.py

# 或者激活虚拟环境后运行
source .venv/bin/activate  # Linux/macOS
# .venv\Scripts\activate     # Windows
python main.py
```

### 4. 安装为可执行命令（可选）

```bash
# 安装项目到虚拟环境
uv sync

# 现在可以直接运行
uv run gomoku
```

## 依赖管理

### 添加新依赖

```bash
# 添加运行时依赖
uv add <package-name>

# 添加开发依赖
uv add --dev <package-name>

# 示例：添加numpy
uv add numpy
```

### 移除依赖

```bash
uv remove <package-name>
```

### 查看已安装的包

```bash
uv pip list
```

### 更新依赖

```bash
# 更新所有依赖
uv sync --upgrade

# 更新特定包
uv add <package-name> --upgrade
```

## 项目结构

```
五子棋游戏/
├── .venv/              # 虚拟环境（由uv创建）
├── .python-version     # Python版本锁定
├── pyproject.toml      # 项目配置和依赖管理
├── uv.lock             # uv依赖锁定文件
├── main.py            # 主程序入口
├── game.py            # 游戏核心逻辑
├── board.py           # 棋盘类
├── player.py          # 玩家类
├── ai.py              # AI对战逻辑
├── utils.py           # 工具函数
└── README.md          # 项目说明
```

## 常用命令

```bash
# 检查uv版本
uv --version

# 查看项目信息
uv info

# 清理缓存
uv cache clean

# 查看虚拟环境路径
uv venv --show-path

# 重新创建虚拟环境
uv venv --force
```

## 优势

使用uv管理Python环境的优势：

1. **极快的速度** - 比pip快10-100倍
2. **依赖解析** - 智能解决依赖冲突
3. **环境隔离** - 自动创建和管理虚拟环境
4. **锁定文件** - 确保环境一致性
5. **简单易用** - 统一的命令接口

## 故障排除

### 虚拟环境问题

```bash
# 删除并重新创建虚拟环境
rm -rf .venv
uv sync
```

### Python版本问题

```bash
# 指定Python版本
uv python install 3.11
uv sync
```

### 依赖冲突

```bash
# 强制更新依赖
uv sync --upgrade --force
```