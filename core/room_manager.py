#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房间管理器模块
作者: AI Assistant
描述: 管理游戏房间，支持多人在线游戏
"""

import uuid
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from .game_engine import GameEngine, GameMode
from player import Player

class GameRoom:
    """游戏房间类"""
    
    def __init__(self, room_id: str, game_mode: str = 'pvp', ai_difficulty: str = 'medium', max_players: int = 2):
        self.room_id = room_id
        self.game_engine = GameEngine()
        self.players: List[Player] = []
        self.spectators: List[Player] = []
        # PVE模式只需要1个真实玩家，PVP模式需要2个玩家
        self.max_players = 1 if game_mode == 'pve' else max_players
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.is_active = True
        self.game_mode = game_mode
        self.ai_difficulty = ai_difficulty
    
    def add_player(self, player: Player) -> bool:
        """添加玩家到房间"""
        if len(self.players) >= self.max_players:
            return False
        
        self.players.append(player)
        self.last_activity = datetime.now()
        
        # 根据游戏模式决定何时开始游戏
        if self.game_mode == 'pve':
            # PVE模式：只需要1个真实玩家就可以开始
            if len(self.players) == 1:
                player_names = [p.name for p in self.players]
                self.game_engine.setup_game('pve', player_names[0], ai_difficulty=self.ai_difficulty)
        elif self.game_mode == 'pvp':
            # PVP模式：需要2个真实玩家
            if len(self.players) == self.max_players:
                player_names = [p.name for p in self.players]
                self.game_engine.setup_game('pvp', player_names[0], player_names[1] if len(player_names) > 1 else "玩家2")
        
        return True
    
    def remove_player(self, player_id: str) -> bool:
        """从房间移除玩家"""
        for i, player in enumerate(self.players):
            if hasattr(player, 'id') and player.id == player_id:
                self.players.pop(i)
                self.last_activity = datetime.now()
                return True
        return False
    
    def add_spectator(self, player: Player) -> bool:
        """添加观众"""
        self.spectators.append(player)
        self.last_activity = datetime.now()
        return True
    
    def remove_spectator(self, player_id: str) -> bool:
        """移除观众"""
        for i, spectator in enumerate(self.spectators):
            if hasattr(spectator, 'id') and spectator.id == player_id:
                self.spectators.pop(i)
                return True
        return False
    
    def is_full(self) -> bool:
        """检查房间是否已满"""
        return len(self.players) >= self.max_players
    
    def is_empty(self) -> bool:
        """检查房间是否为空"""
        return len(self.players) == 0 and len(self.spectators) == 0
    
    def get_player_count(self) -> int:
        """获取玩家数量"""
        return len(self.players)
    
    def get_spectator_count(self) -> int:
        """获取观众数量"""
        return len(self.spectators)
    
    def update_activity(self) -> None:
        """更新房间活动时间"""
        self.last_activity = datetime.now()
    
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """检查房间是否过期"""
        return datetime.now() - self.last_activity > timedelta(minutes=timeout_minutes)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'room_id': self.room_id,
            'game_mode': self.game_mode,
            'ai_difficulty': self.ai_difficulty,
            'max_players': self.max_players,
            'current_players': len(self.players),
            'spectators': len(self.spectators),
            'is_full': self.is_full(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'players': [{
                'id': getattr(p, 'id', ''),
                'name': p.name,
                'symbol': p.symbol,
                'is_ai': p.is_ai
            } for p in self.players],
            'game_info': self.game_engine.get_game_info() if self.game_engine else None
        }

class RoomManager:
    """房间管理器"""
    
    def __init__(self):
        self.rooms: Dict[str, GameRoom] = {}
        self.player_room_map: Dict[str, str] = {}  # player_id -> room_id
    
    def create_room(self, game_mode: str = 'pvp', ai_difficulty: str = 'medium', max_players: int = 2) -> str:
        """创建新房间"""
        room_id = str(uuid.uuid4())[:8]  # 使用短ID
        # PVE模式下强制设置max_players为1
        actual_max_players = 1 if game_mode == 'pve' else max_players
        room = GameRoom(room_id, game_mode, ai_difficulty, actual_max_players)
        self.rooms[room_id] = room
        return room_id
    
    def get_room(self, room_id: str) -> Optional[GameRoom]:
        """获取房间"""
        return self.rooms.get(room_id)
    
    def room_exists(self, room_id: str) -> bool:
        """检查房间是否存在"""
        return room_id in self.rooms
    
    def is_room_full(self, room_id: str) -> bool:
        """检查房间是否已满"""
        room = self.get_room(room_id)
        return room.is_full() if room else True
    
    def add_player_to_room(self, room_id: str, player: Player) -> bool:
        """将玩家添加到房间"""
        room = self.get_room(room_id)
        if not room:
            return False
        
        if room.add_player(player):
            # 记录玩家所在房间
            if hasattr(player, 'id'):
                self.player_room_map[player.id] = room_id
            return True
        return False
    
    def remove_player_from_room(self, player_id: str) -> bool:
        """从房间移除玩家"""
        room_id = self.player_room_map.get(player_id)
        if not room_id:
            return False
        
        room = self.get_room(room_id)
        if room and room.remove_player(player_id):
            del self.player_room_map[player_id]
            
            # 如果房间为空，删除房间
            if room.is_empty():
                self.delete_room(room_id)
            
            return True
        return False
    
    def get_player_room(self, player_id: str) -> Optional[str]:
        """获取玩家所在房间ID"""
        return self.player_room_map.get(player_id)
    
    def delete_room(self, room_id: str) -> bool:
        """删除房间"""
        if room_id in self.rooms:
            # 清理玩家房间映射
            players_to_remove = []
            for player_id, mapped_room_id in self.player_room_map.items():
                if mapped_room_id == room_id:
                    players_to_remove.append(player_id)
            
            for player_id in players_to_remove:
                del self.player_room_map[player_id]
            
            del self.rooms[room_id]
            return True
        return False
    
    def get_available_rooms(self, game_mode: str = None) -> List[Dict[str, Any]]:
        """获取可用房间列表"""
        available_rooms = []
        for room in self.rooms.values():
            if not room.is_full() and room.is_active:
                if game_mode is None or room.game_mode == game_mode:
                    available_rooms.append(room.to_dict())
        return available_rooms
    
    def get_room_count(self) -> int:
        """获取房间总数"""
        return len(self.rooms)
    
    def get_active_room_count(self) -> int:
        """获取活跃房间数"""
        return len([room for room in self.rooms.values() if room.is_active])
    
    def cleanup_expired_rooms(self, timeout_minutes: int = 30) -> int:
        """清理过期房间"""
        expired_rooms = []
        for room_id, room in self.rooms.items():
            if room.is_expired(timeout_minutes) or room.is_empty():
                expired_rooms.append(room_id)
        
        for room_id in expired_rooms:
            self.delete_room(room_id)
        
        return len(expired_rooms)
    
    def get_room_stats(self) -> Dict[str, Any]:
        """获取房间统计信息"""
        total_rooms = len(self.rooms)
        active_rooms = self.get_active_room_count()
        total_players = sum(room.get_player_count() for room in self.rooms.values())
        total_spectators = sum(room.get_spectator_count() for room in self.rooms.values())
        
        return {
            'total_rooms': total_rooms,
            'active_rooms': active_rooms,
            'total_players': total_players,
            'total_spectators': total_spectators,
            'rooms_by_mode': {
                'pvp': len([r for r in self.rooms.values() if r.game_mode == 'pvp']),
                'pve': len([r for r in self.rooms.values() if r.game_mode == 'pve'])
            }
        }