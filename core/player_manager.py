#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玩家管理器模块
作者: AI Assistant
描述: 管理在线玩家，处理玩家会话和状态
"""

import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from player import Player

class OnlinePlayer(Player):
    """在线玩家类，扩展基础玩家类"""
    
    def __init__(self, player_id: str, name: str, symbol: int = 0, is_ai: bool = False):
        super().__init__(name, symbol, is_ai)
        self.id = player_id
        self.session_id: Optional[str] = None
        self.room_id: Optional[str] = None
        self.connected_at = datetime.now()
        self.last_activity = datetime.now()
        self.is_online = True
        self.stats = {
            'games_played': 0,
            'games_won': 0,
            'games_lost': 0,
            'games_drawn': 0,
            'total_moves': 0,
            'average_move_time': 0.0
        }
    
    def update_activity(self) -> None:
        """更新玩家活动时间"""
        self.last_activity = datetime.now()
    
    def set_session(self, session_id: str) -> None:
        """设置会话ID"""
        self.session_id = session_id
        self.update_activity()
    
    def set_room(self, room_id: str) -> None:
        """设置房间ID"""
        self.room_id = room_id
        self.update_activity()
    
    def disconnect(self) -> None:
        """断开连接"""
        self.is_online = False
        self.session_id = None
    
    def reconnect(self, session_id: str) -> None:
        """重新连接"""
        self.is_online = True
        self.session_id = session_id
        self.update_activity()
    
    def is_expired(self, timeout_minutes: int = 30) -> bool:
        """检查玩家是否过期（长时间未活动）"""
        return datetime.now() - self.last_activity > timedelta(minutes=timeout_minutes)
    
    def update_game_stats(self, result: str, moves_count: int = 0, game_duration: float = 0.0) -> None:
        """更新游戏统计"""
        self.stats['games_played'] += 1
        self.stats['total_moves'] += moves_count
        
        if result == 'win':
            self.stats['games_won'] += 1
        elif result == 'lose':
            self.stats['games_lost'] += 1
        elif result == 'draw':
            self.stats['games_drawn'] += 1
        
        # 更新平均移动时间
        if moves_count > 0 and game_duration > 0:
            avg_move_time = game_duration / moves_count
            total_moves = self.stats['total_moves']
            current_avg = self.stats['average_move_time']
            self.stats['average_move_time'] = ((current_avg * (total_moves - moves_count)) + (avg_move_time * moves_count)) / total_moves
    
    def get_win_rate(self) -> float:
        """获取胜率"""
        total_games = self.stats['games_played']
        if total_games == 0:
            return 0.0
        return self.stats['games_won'] / total_games
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'symbol': self.symbol,
            'is_ai': self.is_ai,
            'is_online': self.is_online,
            'room_id': self.room_id,
            'connected_at': self.connected_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'stats': self.stats.copy(),
            'win_rate': self.get_win_rate()
        }

class PlayerManager:
    """玩家管理器"""
    
    def __init__(self):
        self.players: Dict[str, OnlinePlayer] = {}  # player_id -> OnlinePlayer
        self.session_player_map: Dict[str, str] = {}  # session_id -> player_id
        self.name_player_map: Dict[str, str] = {}  # player_name -> player_id
    
    def create_player(self, player_id: str, name: str, room_id: str = None) -> OnlinePlayer:
        """创建新玩家"""
        # 检查名称是否已存在
        if name in self.name_player_map:
            existing_player_id = self.name_player_map[name]
            existing_player = self.players.get(existing_player_id)
            if existing_player and existing_player.is_online:
                # 如果同名玩家在线，添加后缀
                name = f"{name}_{len([p for p in self.players.values() if p.name.startswith(name)])}"
        
        player = OnlinePlayer(player_id, name)
        if room_id:
            player.set_room(room_id)
        
        self.players[player_id] = player
        self.name_player_map[name] = player_id
        
        return player
    
    def get_player(self, player_id: str) -> Optional[OnlinePlayer]:
        """获取玩家"""
        return self.players.get(player_id)
    
    def get_player_by_session(self, session_id: str) -> Optional[OnlinePlayer]:
        """通过会话ID获取玩家"""
        player_id = self.session_player_map.get(session_id)
        if player_id:
            return self.get_player(player_id)
        return None
    
    def get_player_by_name(self, name: str) -> Optional[OnlinePlayer]:
        """通过名称获取玩家"""
        player_id = self.name_player_map.get(name)
        if player_id:
            return self.get_player(player_id)
        return None
    
    def set_player_session(self, player_id: str, session_id: str) -> bool:
        """设置玩家会话"""
        player = self.get_player(player_id)
        if player:
            # 清理旧的会话映射
            if player.session_id and player.session_id in self.session_player_map:
                del self.session_player_map[player.session_id]
            
            player.set_session(session_id)
            self.session_player_map[session_id] = player_id
            return True
        return False
    
    def remove_player(self, player_id: str) -> bool:
        """移除玩家"""
        player = self.get_player(player_id)
        if player:
            # 清理映射
            if player.session_id and player.session_id in self.session_player_map:
                del self.session_player_map[player.session_id]
            
            if player.name in self.name_player_map:
                del self.name_player_map[player.name]
            
            del self.players[player_id]
            return True
        return False
    
    def remove_player_by_session(self, session_id: str) -> bool:
        """通过会话ID移除玩家"""
        player = self.get_player_by_session(session_id)
        if player:
            return self.remove_player(player.id)
        return False
    
    def disconnect_player(self, player_id: str) -> bool:
        """断开玩家连接"""
        player = self.get_player(player_id)
        if player:
            player.disconnect()
            # 清理会话映射
            if player.session_id and player.session_id in self.session_player_map:
                del self.session_player_map[player.session_id]
            return True
        return False
    
    def disconnect_player_by_session(self, session_id: str) -> bool:
        """通过会话ID断开玩家连接"""
        player = self.get_player_by_session(session_id)
        if player:
            return self.disconnect_player(player.id)
        return False
    
    def reconnect_player(self, player_id: str, session_id: str) -> bool:
        """重新连接玩家"""
        player = self.get_player(player_id)
        if player:
            player.reconnect(session_id)
            self.session_player_map[session_id] = player_id
            return True
        return False
    
    def get_online_players(self) -> List[OnlinePlayer]:
        """获取在线玩家列表"""
        return [player for player in self.players.values() if player.is_online]
    
    def get_players_in_room(self, room_id: str) -> List[OnlinePlayer]:
        """获取指定房间的玩家"""
        return [player for player in self.players.values() if player.room_id == room_id]
    
    def update_player_activity(self, player_id: str) -> bool:
        """更新玩家活动时间"""
        player = self.get_player(player_id)
        if player:
            player.update_activity()
            return True
        return False
    
    def cleanup_expired_players(self, timeout_minutes: int = 30) -> int:
        """清理过期玩家"""
        expired_players = []
        for player_id, player in self.players.items():
            if not player.is_online or player.is_expired(timeout_minutes):
                expired_players.append(player_id)
        
        for player_id in expired_players:
            self.remove_player(player_id)
        
        return len(expired_players)
    
    def get_player_count(self) -> int:
        """获取玩家总数"""
        return len(self.players)
    
    def get_online_player_count(self) -> int:
        """获取在线玩家数"""
        return len(self.get_online_players())
    
    def get_player_stats(self) -> Dict[str, Any]:
        """获取玩家统计信息"""
        online_players = self.get_online_players()
        total_games = sum(player.stats['games_played'] for player in self.players.values())
        
        return {
            'total_players': len(self.players),
            'online_players': len(online_players),
            'total_games_played': total_games,
            'average_win_rate': sum(player.get_win_rate() for player in self.players.values()) / len(self.players) if self.players else 0,
            'top_players': sorted(
                [player.to_dict() for player in self.players.values()],
                key=lambda x: (x['stats']['games_won'], x['win_rate']),
                reverse=True
            )[:10]
        }
    
    def search_players(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索玩家"""
        results = []
        query_lower = query.lower()
        
        for player in self.players.values():
            if query_lower in player.name.lower():
                results.append(player.to_dict())
                if len(results) >= limit:
                    break
        
        return results