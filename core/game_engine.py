#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏引擎核心模块
作者: AI Assistant
描述: 五子棋游戏的核心逻辑引擎，支持多种界面模式
"""

import copy
from typing import Optional, Tuple, Dict, List, Any
from enum import Enum

# 导入原有模块
from board import Board
from player import Player
from ai import AIPlayer

class GameStatus(Enum):
    """游戏状态枚举"""
    WAITING = "waiting"  # 等待玩家
    PLAYING = "playing"  # 游戏进行中
    FINISHED = "finished"  # 游戏结束
    PAUSED = "paused"  # 游戏暂停

class GameMode(Enum):
    """游戏模式枚举"""
    PVP = "pvp"  # 玩家对战
    PVE = "pve"  # 人机对战
    SPECTATE = "spectate"  # 观战模式

class GameEngine:
    """游戏引擎核心类"""
    
    def __init__(self, board_size: int = 15):
        self.board = Board(board_size)
        self.players: List[Player] = []
        self.current_player_index = 0
        self.game_status = GameStatus.WAITING
        self.game_mode = GameMode.PVP
        self.winner: Optional[Player] = None
        self.game_history: List[Dict] = []  # 游戏历史记录
        self.move_count = 0
        self.ai_difficulty = 'medium'
        
    def setup_game(self, mode: str, player1_name: str = "玩家1", 
                   player2_name: str = "玩家2", ai_difficulty: str = 'medium') -> bool:
        """设置游戏模式和玩家"""
        try:
            self.ai_difficulty = ai_difficulty
            
            if mode == 'pvp' or mode == GameMode.PVP:
                self.game_mode = GameMode.PVP
                self.players = [
                    Player(player1_name, 1),
                    Player(player2_name, 2)
                ]
            elif mode == 'pve' or mode == GameMode.PVE:
                self.game_mode = GameMode.PVE
                self.players = [
                    Player(player1_name, 1),
                    AIPlayer("AI", 2, ai_difficulty)
                ]
            else:
                raise ValueError(f"不支持的游戏模式: {mode}")
            
            self.game_status = GameStatus.PLAYING
            self.current_player_index = 0
            self.winner = None
            self.game_history = []
            self.move_count = 0
            
            return True
        except Exception as e:
            print(f"设置游戏失败: {e}")
            return False
    
    def add_player(self, player: Player) -> bool:
        """添加玩家到游戏"""
        if len(self.players) >= 2:
            return False
        
        # 设置玩家符号
        player.symbol = len(self.players) + 1
        self.players.append(player)
        
        # 如果有两个玩家，开始游戏
        if len(self.players) == 2:
            self.game_status = GameStatus.PLAYING
        
        return True
    
    def make_move(self, player_id: str, row: int, col: int) -> Dict[str, Any]:
        """执行落子操作"""
        try:
            # 检查游戏状态
            if self.game_status != GameStatus.PLAYING:
                return {'success': False, 'error': '游戏未在进行中'}
            
            # 检查是否轮到该玩家
            current_player = self.get_current_player()
            if not current_player:
                return {'success': False, 'error': '当前没有玩家'}
            
            # 对于AI玩家，使用name进行匹配；对于普通玩家，使用id进行匹配
            player_match = False
            if hasattr(current_player, 'is_ai') and current_player.is_ai:
                player_match = (current_player.name == player_id or current_player.id == player_id)
            else:
                player_match = (hasattr(current_player, 'id') and current_player.id == player_id)
            
            if not player_match:
                return {'success': False, 'error': '不是你的回合'}
            
            # 检查落子位置是否有效
            if not self.board.is_valid_move(row, col):
                return {'success': False, 'error': '无效的落子位置'}
            
            # 执行落子
            if self.board.make_move(row, col, current_player.symbol):
                # 记录移动历史
                move_record = {
                    'player': current_player.name,
                    'symbol': current_player.symbol,
                    'row': row,
                    'col': col,
                    'move_number': self.move_count + 1
                }
                self.game_history.append(move_record)
                self.move_count += 1
                
                # 检查是否获胜
                if self.board.check_winner(row, col, current_player.symbol):
                    self.winner = current_player
                    self.game_status = GameStatus.FINISHED
                    return {
                        'success': True,
                        'game_over': True,
                        'winner': current_player.name,
                        'move': move_record
                    }
                
                # 检查是否平局
                if self.board.is_full():
                    self.game_status = GameStatus.FINISHED
                    return {
                        'success': True,
                        'game_over': True,
                        'winner': None,
                        'reason': 'draw',
                        'move': move_record
                    }
                
                # 切换到下一个玩家
                self.current_player_index = (self.current_player_index + 1) % 2
                
                return {
                    'success': True,
                    'game_over': False,
                    'move': move_record,
                    'next_player': self.get_current_player().name
                }
            else:
                return {'success': False, 'error': '落子失败'}
                
        except Exception as e:
            return {'success': False, 'error': f'执行落子时发生错误: {str(e)}'}
    
    def get_ai_move(self) -> Optional[Tuple[int, int]]:
        """获取AI的落子位置"""
        current_player = self.get_current_player()
        if current_player and isinstance(current_player, AIPlayer):
            return current_player.get_move(self.board)
        return None
    
    def undo_move(self) -> bool:
        """悔棋操作"""
        if not self.game_history or self.game_status == GameStatus.FINISHED:
            return False
        
        # 获取最后一步
        last_move = self.game_history.pop()
        
        # 恢复棋盘状态
        self.board.board[last_move['row']][last_move['col']] = 0
        
        # 切换玩家
        self.current_player_index = (self.current_player_index + 1) % 2
        self.move_count -= 1
        
        # 重置游戏状态
        if self.game_status == GameStatus.FINISHED:
            self.game_status = GameStatus.PLAYING
            self.winner = None
        
        return True
    
    def reset_game(self) -> None:
        """重置游戏"""
        self.board = Board(self.board.size)
        self.current_player_index = 0
        self.game_status = GameStatus.PLAYING if len(self.players) == 2 else GameStatus.WAITING
        self.winner = None
        self.game_history = []
        self.move_count = 0
    
    def get_current_player(self) -> Optional[Player]:
        """获取当前玩家"""
        if self.players and 0 <= self.current_player_index < len(self.players):
            return self.players[self.current_player_index]
        return None
    
    def get_board_state(self) -> List[List[int]]:
        """获取棋盘状态"""
        return copy.deepcopy(self.board.board)
    
    def get_game_status(self) -> str:
        """获取游戏状态"""
        return self.game_status.value
    
    def is_game_over(self) -> bool:
        """检查游戏是否结束"""
        return self.game_status == GameStatus.FINISHED
    
    def get_winner(self) -> Optional[str]:
        """获取获胜者"""
        return self.winner.name if self.winner else None
    
    def get_game_over_reason(self) -> str:
        """获取游戏结束原因"""
        if self.winner:
            return f"{self.winner.name} 获胜！"
        elif self.board.is_full():
            return "平局！"
        else:
            return "游戏进行中"
    
    def get_game_info(self) -> Dict[str, Any]:
        """获取游戏信息"""
        return {
            'board_size': self.board.size,
            'game_mode': self.game_mode.value,
            'game_status': self.game_status.value,
            'current_player': self.get_current_player().name if self.get_current_player() else None,
            'move_count': self.move_count,
            'players': [{'name': p.name, 'symbol': p.symbol, 'is_ai': p.is_ai} for p in self.players],
            'winner': self.get_winner(),
            'can_undo': len(self.game_history) > 0 and self.game_status != GameStatus.FINISHED
        }
    
    def get_move_history(self) -> List[Dict]:
        """获取移动历史"""
        return copy.deepcopy(self.game_history)
    
    def get_state(self) -> Dict[str, Any]:
        """获取完整游戏状态"""
        last_move = None
        if self.game_history:
            last_move = self.game_history[-1]
        
        return {
            'board': self.get_board_state(),
            'current_player': self.get_current_player().name if self.get_current_player() else None,
            'game_status': self.get_game_status(),
            'move_count': self.move_count,
            'last_move': last_move,
            'players': [{'name': p.name, 'symbol': p.symbol, 'is_ai': p.is_ai} for p in self.players],
            'winner': self.get_winner(),
            'game_over': self.is_game_over(),
            'draw': self.board.is_full() and not self.winner
        }
    
    def export_game_state(self) -> Dict[str, Any]:
        """导出完整游戏状态"""
        return {
            'board': self.get_board_state(),
            'game_info': self.get_game_info(),
            'move_history': self.get_move_history(),
            'timestamp': self.move_count
        }
    
    def import_game_state(self, state: Dict[str, Any]) -> bool:
        """导入游戏状态"""
        try:
            # 恢复棋盘
            self.board.board = state['board']
            
            # 恢复游戏信息
            game_info = state['game_info']
            self.move_count = game_info['move_count']
            self.game_history = state['move_history']
            
            # 恢复游戏状态
            if game_info['game_status'] == 'finished':
                self.game_status = GameStatus.FINISHED
                # 找到获胜者
                for player in self.players:
                    if player.name == game_info['winner']:
                        self.winner = player
                        break
            else:
                self.game_status = GameStatus.PLAYING
            
            # 恢复当前玩家
            current_player_name = game_info['current_player']
            for i, player in enumerate(self.players):
                if player.name == current_player_name:
                    self.current_player_index = i
                    break
            
            return True
        except Exception as e:
            print(f"导入游戏状态失败: {e}")
            return False