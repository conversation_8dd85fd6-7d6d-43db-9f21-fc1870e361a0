[project]
name = "gomoku"
version = "0.1.0"
description = "A complete Gomoku (Five in a Row) game with AI opponents and human vs human mode"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "Flask>=1.1.0",
    "Flask-SocketIO>=4.0.0",
    "python-socketio>=4.0.0",
    "python-engineio>=3.0.0",
    "Werkzeug>=1.0.0",
    "Jinja2>=2.10.0",
    "MarkupSafe>=1.1.0",
    "ItsDangerous>=1.1.0",
    "Click>=7.0.0",
    "eventlet>=0.25.0",
]

[project.scripts]
gomoku = "main:main"
gomoku-gui = "main_gui:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]
