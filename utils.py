def clear_screen():
    """清屏函数"""
    import os
    os.system('cls' if os.name == 'nt' else 'clear')

def get_game_mode():
    """获取游戏模式"""
    while True:
        print("\n=== 五子棋游戏 ===")
        print("请选择游戏模式:")
        print("1. 双人对战")
        print("2. 人机对战")
        print("3. 退出游戏")
        
        try:
            choice = int(input("请输入选择 (1-3): "))
            if choice in [1, 2, 3]:
                return choice
            else:
                print("请输入1-3之间的数字")
        except ValueError:
            print("输入错误，请输入数字")

def ask_play_again():
    """询问是否再次游戏"""
    while True:
        choice = input("\n是否再来一局？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', 'Y']:
            return True
        elif choice in ['n', 'no', '否', 'N']:
            return False
        else:
            print("请输入 y 或 n")