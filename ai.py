import random
from player import Player

class AIPlayer(Player):
    def __init__(self, name, symbol, difficulty='medium'):
        super().__init__(name, symbol, is_ai=True)
        self.difficulty = difficulty
    
    def get_move(self, board):
        """AI获取最佳落子位置"""
        if self.difficulty == 'easy':
            return self._random_move(board)
        elif self.difficulty == 'medium':
            return self._smart_move(board)
        else:
            return self._advanced_move(board)
    
    def _random_move(self, board):
        """随机落子"""
        empty_positions = board.get_empty_positions()
        return random.choice(empty_positions)
    
    def _smart_move(self, board):
        """智能落子 - 基于简单评分系统"""
        best_score = -1
        best_move = None
        
        for row in range(board.size):
            for col in range(board.size):
                if board.is_valid_move(row, col):
                    score = self._evaluate_position(board, row, col)
                    if score > best_score:
                        best_score = score
                        best_move = (row, col)
        
        return best_move if best_move else self._random_move(board)
    
    def _advanced_move(self, board):
        """高级AI - 使用更复杂的评估函数"""
        # 首先检查是否能获胜
        winning_move = self._find_winning_move(board, self.symbol)
        if winning_move:
            return winning_move
        
        # 检查是否需要阻止对手获胜
        opponent = 1 if self.symbol == 2 else 2
        blocking_move = self._find_winning_move(board, opponent)
        if blocking_move:
            return blocking_move
        
        # 否则使用智能落子
        return self._smart_move(board)
    
    def _find_winning_move(self, board, player):
        """寻找获胜的落子位置"""
        for row in range(board.size):
            for col in range(board.size):
                if board.is_valid_move(row, col):
                    # 临时落子
                    board.board[row][col] = player
                    if board.check_winner(row, col, player):
                        board.board[row][col] = 0  # 撤销
                        return (row, col)
                    board.board[row][col] = 0  # 撤销
        return None
    
    def _evaluate_position(self, board, row, col):
        """评估位置的价值"""
        score = 0
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
        
        # 临时落子进行评估
        board.board[row][col] = self.symbol
        
        for dx, dy in directions:
            line_score = self._evaluate_line(board, row, col, dx, dy, self.symbol)
            score += line_score
        
        # 撤销临时落子
        board.board[row][col] = 0
        
        # 中心位置加分
        center = board.size // 2
        distance_to_center = abs(row - center) + abs(col - center)
        score += max(0, 10 - distance_to_center)
        
        return score
    
    def _evaluate_line(self, board, row, col, dx, dy, player):
        """评估某个方向上的得分"""
        count = 1
        blocked = 0
        
        # 正方向
        r, c = row + dx, col + dy
        while (0 <= r < board.size and 0 <= c < board.size):
            if board.board[r][c] == player:
                count += 1
            elif board.board[r][c] == 0:
                break
            else:
                blocked += 1
                break
            r, c = r + dx, c + dy
        
        # 反方向
        r, c = row - dx, col - dy
        while (0 <= r < board.size and 0 <= c < board.size):
            if board.board[r][c] == player:
                count += 1
            elif board.board[r][c] == 0:
                break
            else:
                blocked += 1
                break
            r, c = r - dx, c - dy
        
        # 根据连子数和阻挡情况计算分数
        if count >= 5:
            return 10000
        elif count == 4 and blocked == 0:
            return 1000
        elif count == 4 and blocked == 1:
            return 100
        elif count == 3 and blocked == 0:
            return 100
        elif count == 3 and blocked == 1:
            return 10
        elif count == 2 and blocked == 0:
            return 10
        else:
            return 1