#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五子棋游戏 - 图形界面版本
作者: AI Assistant
描述: 使用tkinter创建的五子棋游戏图形界面
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui import GomokuGUI

def main():
    """主函数 - 启动图形界面版本的五子棋游戏"""
    try:
        print("正在启动五子棋图形界面...")
        app = GomokuGUI()
        app.run()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保所有必要的模块都已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()