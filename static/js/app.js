// 五子棋大师 - 客户端应用逻辑

// 全局变量
let socket = null;
let gameState = {
    connected: false,
    roomId: null,
    playerId: null,
    playerName: '',
    gameMode: 'pvp',
    board: [],
    currentPlayer: null,
    gameStatus: 'waiting',
    players: [],
    moveHistory: [],
    isMyTurn: false,
    gameStats: {
        onlineGames: 0,
        aiGames: 0,
        totalPlayers: 0
    }
};

// 初始化应用
function initApp() {
    initSocket();
    setupEventListeners();
    loadGameStats();
    
    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get('mode');
    if (mode && window.location.pathname === '/game') {
        handleGameModeFromURL(mode);
    }
}

// 初始化Socket连接
function initSocket() {
    try {
        socket = io();
        
        // 连接事件
        socket.on('connect', onSocketConnect);
        socket.on('disconnect', onSocketDisconnect);
        socket.on('connect_error', onSocketError);
        
        // 游戏事件
        socket.on('player_joined', onPlayerJoined);
        socket.on('player_left', onPlayerLeft);
        socket.on('game_update', onGameUpdate);
        socket.on('game_over', onGameOver);
        socket.on('game_reset', onGameReset);
        socket.on('move_error', onMoveError);
        socket.on('room_update', onRoomUpdate);
        
        // 统计事件
        socket.on('stats_update', onStatsUpdate);
        
    } catch (error) {
        console.error('Socket初始化失败:', error);
        updateConnectionStatus('disconnected');
    }
}

// 设置事件监听器
function setupEventListeners() {
    // 页面卸载时断开连接
    window.addEventListener('beforeunload', () => {
        if (socket) {
            socket.disconnect();
        }
    });
    
    // 网络状态变化
    window.addEventListener('online', () => {
        showMessage('网络连接已恢复', 'success');
        if (socket && !socket.connected) {
            socket.connect();
        }
    });
    
    window.addEventListener('offline', () => {
        showMessage('网络连接已断开', 'warning');
        updateConnectionStatus('disconnected');
    });
}

// Socket事件处理
function onSocketConnect() {
    console.log('Socket连接成功');
    gameState.connected = true;
    updateConnectionStatus('connected');
    
    // 如果有房间ID，重新加入房间
    if (gameState.roomId && gameState.playerId) {
        socket.emit('join_game_room', {
            room_id: gameState.roomId,
            player_id: gameState.playerId
        });
    }
}

function onSocketDisconnect() {
    console.log('Socket连接断开');
    gameState.connected = false;
    updateConnectionStatus('disconnected');
    showMessage('连接已断开，正在尝试重连...', 'warning');
}

function onSocketError(error) {
    console.error('Socket连接错误:', error);
    updateConnectionStatus('disconnected');
    showMessage('连接失败，请检查网络', 'danger');
}

function onPlayerJoined(data) {
    console.log('玩家加入:', data);
    showMessage(`${data.player_name} 加入了游戏`, 'info');
    
    // 更新玩家列表
    if (typeof updatePlayersInfo === 'function') {
        updatePlayersInfo(data.players);
    }
}

function onPlayerLeft(data) {
    console.log('玩家离开:', data);
    showMessage(`${data.player_name} 离开了游戏`, 'warning');
    
    // 更新玩家列表
    if (typeof updatePlayersInfo === 'function') {
        updatePlayersInfo(data.players);
    }
}

function onGameUpdate(data) {
    console.log('游戏更新:', data);
    
    // 更新游戏状态
    if (data.board) {
        gameState.board = data.board;
    }
    if (data.current_player) {
        gameState.currentPlayer = data.current_player;
    }
    if (data.game_status) {
        gameState.gameStatus = data.game_status;
    }
    
    // 更新UI
    if (typeof updateBoardDisplay === 'function') {
        updateBoardDisplay();
    }
    if (typeof updateGameStatus === 'function') {
        updateGameStatus();
    }
    
    // 添加移动历史
    if (data.last_move && typeof addMoveToHistory === 'function') {
        addMoveToHistory(data.last_move);
    }
    
    // 播放音效
    if (data.last_move) {
        playMoveSound();
    }
}

function onGameOver(data) {
    console.log('游戏结束:', data);
    gameState.gameStatus = 'finished';
    
    if (typeof showGameResult === 'function') {
        showGameResult(data.winner, data.reason);
    }
    
    // 播放结束音效
    if (data.winner === gameState.playerName) {
        playWinSound();
    } else if (data.winner) {
        playLoseSound();
    } else {
        playDrawSound();
    }
}

function onGameReset(data) {
    console.log('游戏重置:', data);
    
    // 重置游戏状态
    gameState.board = data.board || [];
    gameState.currentPlayer = data.current_player;
    gameState.gameStatus = data.game_status || 'waiting';
    gameState.moveHistory = [];
    
    // 更新UI
    if (typeof updateBoardDisplay === 'function') {
        updateBoardDisplay();
    }
    if (typeof updateGameStatus === 'function') {
        updateGameStatus();
    }
    if (typeof clearMoveHistory === 'function') {
        clearMoveHistory();
    }
    if (typeof hideGameResult === 'function') {
        hideGameResult();
    }
    
    showMessage('游戏已重新开始', 'success');
}

function onMoveError(data) {
    console.log('移动错误:', data);
    showMessage(data.message || '移动无效', 'danger');
}

function onRoomUpdate(data) {
    console.log('房间更新:', data);
    
    // 更新房间信息
    if (data.room_info && typeof updateRoomInfo === 'function') {
        updateRoomInfo(data.room_info);
    }
}

function onStatsUpdate(data) {
    console.log('统计更新:', data);
    gameState.gameStats = { ...gameState.gameStats, ...data };
    updateStatsDisplay();
}

// 更新连接状态
function updateConnectionStatus(status) {
    const statusElement = document.getElementById('connectionStatus');
    if (!statusElement) return;
    
    const indicator = statusElement.querySelector('.connection-indicator');
    const text = statusElement.querySelector('.status-text');
    
    if (indicator) {
        indicator.className = `connection-indicator ${status}`;
    }
    
    if (text) {
        const statusTexts = {
            'connected': '已连接',
            'connecting': '连接中',
            'disconnected': '已断开'
        };
        text.textContent = statusTexts[status] || status;
    }
}

// 显示消息
function showMessage(message, type = 'info', duration = 5000) {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type} alert-dismissible fade show message-toast`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 400px;
        animation: slideInRight 0.3s ease-out;
    `;
    
    messageDiv.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getMessageIcon(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.appendChild(messageDiv);
    
    // 自动移除
    if (duration > 0) {
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, duration);
    }
}

// 获取消息图标
function getMessageIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 显示/隐藏加载状态
function showLoading(show = true) {
    const loadingElements = document.querySelectorAll('.loading');
    loadingElements.forEach(element => {
        element.style.display = show ? 'flex' : 'none';
    });
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showMessage('已复制到剪贴板', 'success', 2000);
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

// 备用复制方法
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showMessage('已复制到剪贴板', 'success', 2000);
    } catch (err) {
        console.error('复制失败:', err);
        showMessage('复制失败，请手动复制', 'danger');
    }
    
    document.body.removeChild(textArea);
}

// 加载游戏统计
function loadGameStats() {
    fetch('/api/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                gameState.gameStats = data.stats;
                updateStatsDisplay();
            }
        })
        .catch(error => {
            console.error('加载统计失败:', error);
        });
}

// 更新统计显示
function updateStatsDisplay() {
    const stats = gameState.gameStats;
    
    // 更新首页统计
    const onlineGamesElement = document.getElementById('onlineGames');
    const aiGamesElement = document.getElementById('aiGames');
    const totalPlayersElement = document.getElementById('totalPlayers');
    
    if (onlineGamesElement) {
        onlineGamesElement.textContent = stats.onlineGames || 0;
    }
    if (aiGamesElement) {
        aiGamesElement.textContent = stats.aiGames || 0;
    }
    if (totalPlayersElement) {
        totalPlayersElement.textContent = stats.totalPlayers || 0;
    }
}

// 处理URL中的游戏模式
function handleGameModeFromURL(mode) {
    const gameModeSelect = document.getElementById('gameMode');
    if (gameModeSelect) {
        gameModeSelect.value = mode;
        if (typeof updateGameModeUI === 'function') {
            updateGameModeUI();
        }
    }
}

// 音效播放
function playMoveSound() {
    playSound('move');
}

function playWinSound() {
    playSound('win');
}

function playLoseSound() {
    playSound('lose');
}

function playDrawSound() {
    playSound('draw');
}

function playSound(type) {
    // 简单的音效实现
    try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        // 不同类型的音效
        const soundConfig = {
            'move': { frequency: 800, duration: 0.1 },
            'win': { frequency: 1000, duration: 0.5 },
            'lose': { frequency: 400, duration: 0.5 },
            'draw': { frequency: 600, duration: 0.3 }
        };
        
        const config = soundConfig[type] || soundConfig.move;
        
        oscillator.frequency.setValueAtTime(config.frequency, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + config.duration);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + config.duration);
    } catch (error) {
        // 音效播放失败时静默处理
        console.log('音效播放失败:', error);
    }
}

// 工具函数
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function formatDate(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initApp();
    
    // 定期更新统计
    setInterval(loadGameStats, 30000); // 每30秒更新一次
});

// 导出全局函数供其他脚本使用
window.gameApp = {
    gameState,
    socket,
    showMessage,
    showLoading,
    copyToClipboard,
    updateConnectionStatus,
    playMoveSound,
    playWinSound,
    playLoseSound,
    playDrawSound
};