/* 五子棋大师 - 自定义样式 */

:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --board-color: #f4d03f;
    --board-line-color: #8b4513;
}

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

.container-fluid {
    padding: 0;
}

/* 导航栏样式 */
.navbar {
    background: rgba(44, 62, 80, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid var(--accent-color);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.3s ease;
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #2980b9);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #229954);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, var(--success-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22, var(--warning-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c0392b);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, var(--danger-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* 表单样式 */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-1px);
}

.form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 16px;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

/* 警告框样式 */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, rgba(39, 174, 96, 0.1), rgba(34, 153, 84, 0.1));
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1));
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.1), rgba(230, 126, 34, 0.1));
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1));
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* 页脚样式 */
.footer {
    background: var(--dark-color);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: white;
}

/* 加载动画 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 统计卡片 */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: var(--secondary-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

/* 特色卡片 */
.feature-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.feature-description {
    color: #6c757d;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .feature-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .feature-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.85rem;
    }
    
    .stats-number {
        font-size: 1.75rem;
    }
    
    .feature-icon {
        font-size: 2rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: 6px;
    padding: 8px 12px;
}

/* 模态框样式 */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px 15px 0 0;
    border-bottom: none;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: none;
    padding: 1rem 2rem 2rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}

/* 棋盘样式 */
.game-board {
    background: linear-gradient(135deg, #d4af37 0%, #ffd700 50%, #d4af37 100%);
    border: 4px solid #8b4513;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 
        0 15px 35px rgba(0,0,0,0.3),
        inset 0 2px 5px rgba(255,255,255,0.2),
        inset 0 -2px 5px rgba(0,0,0,0.1);
    position: relative;
    overflow: hidden;
}

.game-board::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0,0,0,0.05) 0%, transparent 50%);
    pointer-events: none;
}

.board-grid {
    display: grid;
    grid-template-columns: repeat(15, 1fr);
    grid-template-rows: repeat(15, 1fr);
    gap: 2px;
    background: linear-gradient(45deg, #8b4513 0%, #a0522d 100%);
    padding: 15px;
    border-radius: 10px;
    aspect-ratio: 1;
    max-width: 650px;
    margin: 0 auto;
    box-shadow: inset 0 2px 10px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.board-cell {
    background: linear-gradient(135deg, #f4d03f 0%, #f7dc6f 50%, #f4d03f 100%);
    border: 1px solid rgba(139, 69, 19, 0.2);
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 2px;
}

.board-cell:hover {
    background: linear-gradient(135deg, #fff 0%, #f7dc6f 50%, #fff 100%);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    z-index: 2;
}

.board-cell.disabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.board-cell.disabled:hover {
    transform: none;
    box-shadow: none;
}

.stone {
    width: 85%;
    height: 85%;
    border-radius: 50%;
    box-shadow: 
        0 3px 8px rgba(0,0,0,0.4),
        inset 0 1px 3px rgba(255,255,255,0.3);
    transition: all 0.3s ease;
    position: relative;
    animation: placeStone 0.3s ease-out;
}

.stone::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 15%;
    width: 30%;
    height: 30%;
    border-radius: 50%;
    background: rgba(255,255,255,0.4);
    filter: blur(2px);
}

.stone.black {
    background: radial-gradient(circle at 35% 35%, #666 0%, #222 50%, #000 100%);
    border: 2px solid #111;
}

.stone.white {
    background: radial-gradient(circle at 35% 35%, #fff 0%, #f0f0f0 50%, #ddd 100%);
    border: 2px solid #bbb;
}

.stone.last-move {
    animation: placeStone 0.6s ease-out;
    box-shadow: 
        0 3px 8px rgba(0,0,0,0.4),
        inset 0 1px 3px rgba(255,255,255,0.3),
        0 0 20px rgba(255, 215, 0, 0.8);
    border: 3px solid #ffd700;
    animation: pulse 1.5s infinite;
}

/* 棋子放置动画 */
@keyframes placeStone {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 215, 0, 1), 0 4px 8px rgba(0, 0, 0, 0.3);
    }
}

/* 禁用状态 */
.board-cell.disabled {
    pointer-events: none;
    opacity: 0.7;
}

.board-cell.disabled:hover {
    background-color: transparent;
    transform: none;
}

@keyframes placeStone {
    0% { 
        transform: scale(0) rotate(180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(90deg);
        opacity: 0.8;
    }
    100% { 
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* 棋盘网格线效果 */
.board-grid::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    background-image: 
        repeating-linear-gradient(0deg, rgba(139, 69, 19, 0.3) 0px, rgba(139, 69, 19, 0.3) 1px, transparent 1px, transparent calc(100% / 15)),
        repeating-linear-gradient(90deg, rgba(139, 69, 19, 0.3) 0px, rgba(139, 69, 19, 0.3) 1px, transparent 1px, transparent calc(100% / 15));
    pointer-events: none;
    z-index: 0;
}

/* 连接状态指示器 */
.connection-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.connection-indicator.connected {
    background-color: var(--success-color);
    box-shadow: 0 0 5px rgba(39, 174, 96, 0.5);
}

.connection-indicator.disconnected {
    background-color: var(--danger-color);
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
}

.connection-indicator.connecting {
    background-color: var(--warning-color);
    box-shadow: 0 0 5px rgba(243, 156, 18, 0.5);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}