import tkinter as tk
from tkinter import messagebox, ttk
import math
from board import Board
from player import Player
from ai import AIPlayer
from game import Game

class GomokuGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 五子棋大师 - Gomoku Master")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a2e')
        self.root.resizable(True, True)
        self.root.minsize(1000, 700)
        
        # 设置窗口图标和样式
        try:
            self.root.iconname("Gomo<PERSON>")
        except:
            pass
        
        # 游戏状态
        self.game = None
        self.board_size = 15
        self.cell_size = 35  # 增大棋盘格子
        self.board_margin = 60  # 增大边距
        self.last_move = None  # 记录最后一步
        self.move_history = []  # 移动历史
        
        # 现代化配色方案
        self.colors = {
            'bg_primary': '#1a1a2e',      # 深蓝主背景
            'bg_secondary': '#16213e',     # 次要背景
            'bg_card': '#0f3460',         # 卡片背景
            'board': '#e8dcc6',           # 温暖的棋盘色
            'board_border': '#8b7355',    # 棋盘边框
            'line': '#5d4e37',           # 棋盘线条
            'black': '#2c2c2c',          # 黑子（深灰）
            'white': '#f8f8f8',          # 白子（米白）
            'black_shadow': '#1a1a1a',   # 黑子阴影
            'white_shadow': '#d0d0d0',   # 白子阴影
            'highlight': '#ff6b6b',      # 高亮色
            'accent': '#4ecdc4',         # 强调色
            'accent_hover': '#45b7b8',   # 强调色悬停
            'success': '#2ecc71',        # 成功色
            'warning': '#f39c12',        # 警告色
            'danger': '#e74c3c',         # 危险色
            'info': '#3498db',           # 信息色
            'text_primary': '#ecf0f1',   # 主要文字
            'text_secondary': '#bdc3c7', # 次要文字
            'text_muted': '#7f8c8d',     # 静音文字
            'star_point': '#cd853f'      # 星位点
        }
        
        # 字体配置 - 使用系统通用字体
        self.fonts = {
            'title': ('Helvetica', 24, 'bold'),
            'heading': ('Helvetica', 16, 'bold'),
            'subheading': ('Helvetica', 14, 'bold'),
            'body': ('Helvetica', 13, 'bold'),  # 增加字体大小并加粗
            'button': ('Helvetica', 12, 'bold'),  # 增加按钮字体大小
            'small': ('Helvetica', 11)  # 增加小字体大小
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置现代化用户界面"""
        # 主容器
        main_container = tk.Frame(self.root, bg=self.colors['bg_primary'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 顶部标题区域
        header_frame = tk.Frame(main_container, bg=self.colors['bg_primary'])
        header_frame.pack(fill=tk.X, pady=(0, 25))
        
        # 主标题
        title_label = tk.Label(
            header_frame,
            text="🎯 五子棋大师",
            font=self.fonts['title'],
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        )
        title_label.pack()
        
        # 副标题
        subtitle_label = tk.Label(
            header_frame,
            text="Gomoku Master - 智慧与策略的对决",
            font=self.fonts['body'],
            bg=self.colors['bg_primary'],
            fg=self.colors['text_secondary']
        )
        subtitle_label.pack(pady=(5, 0))
        
        # 主要内容区域
        content_frame = tk.Frame(main_container, bg=self.colors['bg_primary'])
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧控制面板
        left_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'], relief=tk.RAISED, bd=1)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 20))
        left_panel.pack_propagate(False)
        left_panel.config(width=280)
        
        # 控制面板标题
        control_title = tk.Label(
            left_panel,
            text="⚙️ 游戏控制",
            font=self.fonts['heading'],
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        control_title.pack(pady=(20, 15))
        
        # 游戏模式卡片
        mode_card = self.create_enhanced_card(left_panel, "🎮 游戏模式")
        
        self.mode_var = tk.StringVar(value="human")
        
        mode_options = [
            ("👥 双人对战", "human", "与朋友一起享受对弈乐趣"),
            ("🤖 人机对战", "ai", "挑战智能AI，提升棋艺")
        ]
        
        for i, (text, value, desc) in enumerate(mode_options):
            option_container = tk.Frame(mode_card, bg=self.colors['bg_card'])
            option_container.pack(fill=tk.X, pady=(5, 10))
            
            # 选项主体框架
            option_frame = tk.Frame(
                option_container, 
                bg=self.colors['bg_primary'], 
                relief=tk.FLAT,
                bd=1
            )
            option_frame.pack(fill=tk.X, padx=10, pady=2)
            
            radio = tk.Radiobutton(
                option_frame,
                text=text,
                variable=self.mode_var,
                value=value,
                font=self.fonts['body'],
                bg=self.colors['bg_primary'],
                fg=self.colors['text_primary'],
                selectcolor=self.colors['accent'],
                activebackground=self.colors['bg_primary'],
                activeforeground=self.colors['text_primary'],
                indicatoron=True,
                relief=tk.FLAT
            )
            radio.pack(anchor=tk.W, padx=15, pady=(8, 2))
            
            desc_label = tk.Label(
                option_frame,
                text=desc,
                font=self.fonts['small'],
                bg=self.colors['bg_primary'],
                fg=self.colors['text_muted']
            )
            desc_label.pack(anchor=tk.W, padx=35, pady=(0, 8))
            
            # 添加分隔线（除了最后一个选项）
            if i < len(mode_options) - 1:
                separator = tk.Frame(option_container, height=1, bg=self.colors['text_muted'])
                separator.pack(fill=tk.X, padx=20, pady=5)
        
        # AI难度卡片
        difficulty_card = self.create_enhanced_card(left_panel, "🧠 AI难度")
        
        self.difficulty_var = tk.StringVar(value="medium")
        
        difficulties = [
            ("🟢 简单", "easy", "适合新手练习"),
            ("🟡 中等", "medium", "平衡的挑战"),
            ("🔴 困难", "hard", "高级AI对手")
        ]
        
        for i, (text, value, desc) in enumerate(difficulties):
            option_container = tk.Frame(difficulty_card, bg=self.colors['bg_card'])
            option_container.pack(fill=tk.X, pady=(5, 10))
            
            # 选项主体框架
            option_frame = tk.Frame(
                option_container, 
                bg=self.colors['bg_primary'], 
                relief=tk.FLAT,
                bd=1
            )
            option_frame.pack(fill=tk.X, padx=10, pady=2)
            
            radio = tk.Radiobutton(
                option_frame,
                text=text,
                variable=self.difficulty_var,
                value=value,
                font=self.fonts['body'],
                bg=self.colors['bg_primary'],
                fg=self.colors['text_primary'],
                selectcolor=self.colors['accent'],
                activebackground=self.colors['bg_primary'],
                activeforeground=self.colors['text_primary'],
                indicatoron=True,
                relief=tk.FLAT
            )
            radio.pack(anchor=tk.W, padx=15, pady=(8, 2))
            
            desc_label = tk.Label(
                option_frame,
                text=desc,
                font=self.fonts['small'],
                bg=self.colors['bg_primary'],
                fg=self.colors['text_muted']
            )
            desc_label.pack(anchor=tk.W, padx=35, pady=(0, 8))
            
            # 添加分隔线（除了最后一个选项）
            if i < len(difficulties) - 1:
                separator = tk.Frame(option_container, height=1, bg=self.colors['text_muted'])
                separator.pack(fill=tk.X, padx=20, pady=5)
        
        # 按钮区域
        button_card = self.create_enhanced_card(left_panel, "🎯 游戏控制")
        
        # 主要操作按钮
        primary_buttons = [
            ("🚀 开始游戏", self.start_game, self.colors['success']),
            ("🔄 重新开始", self.restart_game, self.colors['warning'])
        ]
        
        # 次要操作按钮
        secondary_buttons = [
            ("↩️ 悔棋", self.undo_move, self.colors['info']),
            ("❌ 退出", self.root.quit, self.colors['danger'])
        ]
        
        # 主要按钮区域
        primary_frame = tk.Frame(button_card, bg=self.colors['bg_card'])
        primary_frame.pack(fill=tk.X, padx=15, pady=(10, 5))
        
        for text, command, color in primary_buttons:
            btn = self.create_enhanced_button(primary_frame, text, command, color, is_primary=True)
            btn.pack(fill=tk.X, pady=3, ipady=5)  # 添加内部垂直填充
        
        # 分隔线
        separator = tk.Frame(button_card, height=2, bg=self.colors['text_muted'])  # 增加分隔线高度
        separator.pack(fill=tk.X, padx=25, pady=15)  # 增加垂直间距
        
        # 次要按钮区域
        secondary_frame = tk.Frame(button_card, bg=self.colors['bg_card'])
        secondary_frame.pack(fill=tk.X, padx=15, pady=(5, 20))  # 增加底部间距
        
        # 创建两列布局
        button_row = tk.Frame(secondary_frame, bg=self.colors['bg_card'])
        button_row.pack(fill=tk.X, pady=5)  # 添加垂直间距
        
        for i, (text, command, color) in enumerate(secondary_buttons):
            btn = self.create_enhanced_button(button_row, text, command, color, is_primary=False)
            btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 8) if i == 0 else (8, 0), ipady=3)  # 增加按钮间距和内部填充
        
        # 中央棋盘区域
        board_container = tk.Frame(content_frame, bg=self.colors['bg_primary'])
        board_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 棋盘标题
        board_title = tk.Label(
            board_container,
            text="🏁 对弈棋盘",
            font=self.fonts['heading'],
            bg=self.colors['bg_primary'],
            fg=self.colors['text_primary']
        )
        board_title.pack(pady=(0, 15))
        
        # 棋盘框架
        board_frame = tk.Frame(
            board_container,
            bg=self.colors['board_border'],
            relief=tk.RAISED,
            bd=3
        )
        board_frame.pack(expand=True)
        
        # 棋盘画布
        canvas_size = self.board_size * self.cell_size + 2 * self.board_margin
        self.canvas = tk.Canvas(
            board_frame,
            width=canvas_size,
            height=canvas_size,
            bg=self.colors['board'],
            highlightthickness=0,
            relief=tk.FLAT
        )
        self.canvas.pack(padx=5, pady=5)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<Motion>", self.on_mouse_move)
        self.canvas.bind("<Leave>", self.on_mouse_leave)
        
        # 右侧信息面板
        right_panel = tk.Frame(content_frame, bg=self.colors['bg_secondary'], relief=tk.RAISED, bd=1)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(20, 0))
        right_panel.pack_propagate(False)
        right_panel.config(width=300)
        
        # 信息面板标题
        info_title = tk.Label(
            right_panel,
            text="📊 游戏信息",
            font=self.fonts['heading'],
            bg=self.colors['bg_secondary'],
            fg=self.colors['text_primary']
        )
        info_title.pack(pady=(20, 15))
        
        # 游戏状态卡片
        status_card = self.create_enhanced_card(right_panel, "🎯 游戏状态")
        
        self.status_label = tk.Label(
            status_card,
            text="点击'开始游戏'开始对弈",
            font=self.fonts['body'],
            bg=self.colors['bg_card'],
            fg=self.colors['text_secondary'],
            wraplength=250,
            justify=tk.CENTER
        )
        self.status_label.pack(pady=15)
        
        # 当前玩家卡片
        player_card = self.create_enhanced_card(right_panel, "👤 当前玩家")
        
        # 玩家信息容器
        player_info_frame = tk.Frame(player_card, bg=self.colors['bg_card'])
        player_info_frame.pack(fill=tk.X, padx=15, pady=10)
        
        # 玩家指示器
        self.player_indicator = tk.Frame(
            player_info_frame, 
            width=20, 
            height=20, 
            bg='black',
            relief=tk.RAISED,
            bd=2
        )
        self.player_indicator.pack(side=tk.LEFT, padx=(0, 10))
        self.player_indicator.pack_propagate(False)
        
        self.current_player_label = tk.Label(
            player_info_frame,
            text="等待开始...",
            font=self.fonts['body'],
            bg=self.colors['bg_card'],
            fg=self.colors['text_primary']
        )
        self.current_player_label.pack(side=tk.LEFT)
        
        # 游戏统计卡片
        stats_card = self.create_enhanced_card(right_panel, "📈 游戏统计")
        
        # 统计信息网格
        stats_grid = tk.Frame(stats_card, bg=self.colors['bg_card'])
        stats_grid.pack(fill=tk.X, padx=15, pady=10)
        
        # 步数统计
        move_frame = tk.Frame(stats_grid, bg=self.colors['bg_primary'], relief=tk.FLAT, bd=1)
        move_frame.pack(fill=tk.X, pady=(0, 8))
        
        tk.Label(
            move_frame,
            text="🎯 步数",
            font=self.fonts['small'],
            bg=self.colors['bg_primary'],
            fg=self.colors['text_muted']
        ).pack(side=tk.LEFT, padx=10, pady=8)
        
        self.move_count_label = tk.Label(
            move_frame,
            text="0",
            font=self.fonts['body'],
            bg=self.colors['bg_primary'],
            fg=self.colors['accent']
        )
        self.move_count_label.pack(side=tk.RIGHT, padx=10, pady=8)
        
        # 时间统计
        time_frame = tk.Frame(stats_grid, bg=self.colors['bg_primary'], relief=tk.FLAT, bd=1)
        time_frame.pack(fill=tk.X)
        
        tk.Label(
            time_frame,
            text="⏱️ 时间",
            font=self.fonts['small'],
            bg=self.colors['bg_primary'],
            fg=self.colors['text_muted']
        ).pack(side=tk.LEFT, padx=10, pady=8)
        
        self.time_label = tk.Label(
            time_frame,
            text="00:00",
            font=self.fonts['body'],
            bg=self.colors['bg_primary'],
            fg=self.colors['accent']
        )
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=8)
        
        # 游戏规则卡片
        rules_card = self.create_enhanced_card(right_panel, "📋 游戏规则")
        
        rules_items = [
            "🔴 黑子先手，轮流落子",
            "🎯 五子连珠即可获胜",
            "↗️ 横竖斜线均有效",
            "👆 点击棋盘交叉点落子",
            "🔄 支持悔棋功能"
        ]
        
        for rule in rules_items:
            rule_frame = tk.Frame(rules_card, bg=self.colors['bg_card'])
            rule_frame.pack(fill=tk.X, padx=15, pady=2)
            
            rule_label = tk.Label(
                rule_frame,
                text=rule,
                font=self.fonts['small'],
                bg=self.colors['bg_card'],
                fg=self.colors['text_muted'],
                anchor=tk.W
            )
            rule_label.pack(fill=tk.X, pady=3)
        
        self.draw_board()
        
    def create_card(self, parent, title):
        """创建卡片容器"""
        card_frame = tk.Frame(
            parent,
            bg=self.colors['bg_card'],
            relief=tk.RAISED,
            bd=1
        )
        card_frame.pack(fill=tk.X, padx=10, pady=8)
        
        # 卡片标题
        title_label = tk.Label(
            card_frame,
            text=title,
            font=self.fonts['heading'],
            bg=self.colors['bg_card'],
            fg=self.colors['text_primary']
        )
        title_label.pack(anchor=tk.W, padx=15, pady=(10, 5))
        
        return card_frame
    
    def create_enhanced_card(self, parent, title):
        """创建增强版卡片容器"""
        # 外层容器
        container = tk.Frame(parent, bg=self.colors['bg_primary'])
        container.pack(fill=tk.X, padx=8, pady=10)
        
        # 卡片主体
        card_frame = tk.Frame(
            container,
            bg=self.colors['bg_card'],
            relief=tk.FLAT,
            bd=0
        )
        card_frame.pack(fill=tk.X, padx=2, pady=2)
        
        # 标题区域
        title_frame = tk.Frame(card_frame, bg=self.colors['accent'])
        title_frame.pack(fill=tk.X)
        
        title_label = tk.Label(
            title_frame,
            text=title,
            font=self.fonts['heading'],
            bg=self.colors['accent'],
            fg='white',
            pady=12
        )
        title_label.pack()
        
        # 内容区域
        content_frame = tk.Frame(card_frame, bg=self.colors['bg_card'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        return content_frame
    
    def create_modern_button(self, parent, text, command, color):
        """创建现代风格按钮"""
        button = tk.Button(
            parent,
            text=text,
            command=command,
            font=self.fonts['body'],
            bg=color,
            fg='white',
            relief=tk.FLAT,
            bd=0,
            padx=20,
            pady=8,
            cursor='hand2'
        )
        button.pack(fill=tk.X, padx=15, pady=5)
        
        # 添加悬停效果
        def on_enter(e):
            button.config(bg=self.darken_color(color))
        
        def on_leave(e):
            button.config(bg=color)
        
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        
        return button
    
    def create_enhanced_button(self, parent, text, command, color, is_primary=True):
        """创建增强版按钮"""
        # 根据按钮类型设置样式
        if is_primary:
            pady = 12
            font = self.fonts['button']  # 使用专门的按钮字体
            relief = tk.RAISED
            bd = 2
            height = 2  # 设置最小高度
        else:
            pady = 8
            font = self.fonts['button']  # 使用专门的按钮字体
            relief = tk.FLAT
            bd = 1
            height = 2  # 设置最小高度
        
        btn = tk.Button(
            parent,
            text=text,
            command=command,
            font=font,
            bg=color,
            fg='white',
            relief=relief,
            bd=bd,
            pady=pady,
            height=height,  # 添加高度参数
            cursor='hand2',
            activebackground=self.darken_color(color),
            activeforeground='white'
        )
        
        # 添加悬停效果
        def on_enter(e):
            btn.config(bg=self.darken_color(color), relief=tk.RAISED if not is_primary else tk.SUNKEN)
        
        def on_leave(e):
            btn.config(bg=color, relief=relief)
        
        btn.bind('<Enter>', on_enter)
        btn.bind('<Leave>', on_leave)
        
        return btn
    
    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            self.colors['success']: '#27ae60',
            self.colors['warning']: '#e67e22',
            self.colors['accent']: '#45b7b8',
            self.colors['danger']: '#c0392b'
        }
        return color_map.get(color, color)
    
    # 添加缺失的颜色定义
    @property
    def info_color(self):
        return '#3498db'
        
    def create_button(self, parent, text, command, row, col):
        """创建样式化按钮"""
        btn = tk.Button(
            parent,
            text=text,
            command=command,
            font=('Arial', 10, 'bold'),
            bg=self.colors.get('button', '#3498db'),
            fg='white',
            relief=tk.FLAT,
            padx=15,
            pady=5,
            cursor='hand2'
        )
        btn.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
        
        # 悬停效果
        def on_enter(e):
            btn.config(bg=self.colors.get('button_hover', '#2980b9'))
        def on_leave(e):
            btn.config(bg=self.colors.get('button', '#3498db'))
            
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        
        return btn
    
    def draw_board(self):
        """绘制棋盘"""
        self.canvas.delete("all")
        
        # 绘制网格线
        for i in range(self.board_size):
            x = self.board_margin + i * self.cell_size
            y = self.board_margin + i * self.cell_size
            
            # 垂直线
            self.canvas.create_line(
                x, self.board_margin,
                x, self.board_margin + (self.board_size - 1) * self.cell_size,
                fill=self.colors['line'], width=1
            )
            
            # 水平线
            self.canvas.create_line(
                self.board_margin, y,
                self.board_margin + (self.board_size - 1) * self.cell_size, y,
                fill=self.colors['line'], width=1
            )
        
        # 绘制天元和星位
        star_positions = [(3, 3), (3, 11), (11, 3), (11, 11), (7, 7)]
        for row, col in star_positions:
            x = self.board_margin + col * self.cell_size
            y = self.board_margin + row * self.cell_size
            self.canvas.create_oval(
                x - 3, y - 3, x + 3, y + 3,
                fill=self.colors['line'], outline=self.colors['line']
            )
    
    def draw_stone(self, row, col, player, highlight=False):
        """绘制棋子"""
        x = self.board_margin + col * self.cell_size
        y = self.board_margin + row * self.cell_size
        radius = self.cell_size // 2 - 2
        
        color = self.colors['black'] if player == 1 else self.colors['white']
        outline_color = self.colors['highlight'] if highlight else color
        outline_width = 3 if highlight else 1
        
        # 绘制棋子阴影
        if not highlight:
            self.canvas.create_oval(
                x - radius + 2, y - radius + 2,
                x + radius + 2, y + radius + 2,
                fill='#888888', outline='#888888'
            )
        
        # 绘制棋子
        stone = self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            fill=color, outline=outline_color, width=outline_width
        )
        
        # 白子添加黑色边框
        if player == 2 and not highlight:
            self.canvas.create_oval(
                x - radius, y - radius,
                x + radius, y + radius,
                fill=color, outline='#333333', width=1
            )
        
        return stone
    
    def on_canvas_click(self, event):
        """处理画布点击事件"""
        if not self.game or self.game.game_over:
            return
        
        # 计算点击的格子坐标
        col = round((event.x - self.board_margin) / self.cell_size)
        row = round((event.y - self.board_margin) / self.cell_size)
        
        # 检查坐标是否有效
        if not (0 <= row < self.board_size and 0 <= col < self.board_size):
            return
        
        # 检查是否为当前玩家的回合
        current_player = self.game.players[self.game.current_player_index]
        if current_player.is_ai:
            return
        
        # 尝试落子
        if self.game.board.make_move(row, col, current_player.symbol):
            self.draw_stone(row, col, current_player.symbol)
            self.update_move_count()
            
            # 检查游戏结果
            if self.game.board.check_winner(row, col, current_player.symbol):
                self.draw_stone(row, col, current_player.symbol, highlight=True)
                self.game.game_over = True
                self.game.winner = current_player
                self.show_game_result()
            elif self.game.board.is_full():
                self.game.game_over = True
                self.show_game_result()
            else:
                # 切换玩家
                self.game.current_player_index = 1 - self.game.current_player_index
                self.update_status()
                
                # 如果下一个玩家是AI，延迟执行AI移动
                if self.game.players[self.game.current_player_index].is_ai:
                    self.root.after(500, self.ai_move)
    
    def ai_move(self):
        """AI移动"""
        if self.game.game_over:
            return
            
        current_player = self.game.players[self.game.current_player_index]
        if not current_player.is_ai:
            return
        
        self.status_label.config(text="AI正在思考...")
        self.root.update()
        
        # 获取AI移动
        row, col = current_player.get_move(self.game.board)
        
        # 执行移动
        if self.game.board.make_move(row, col, current_player.symbol):
            self.draw_stone(row, col, current_player.symbol)
            self.update_move_count()
            
            # 检查游戏结果
            if self.game.board.check_winner(row, col, current_player.symbol):
                self.draw_stone(row, col, current_player.symbol, highlight=True)
                self.game.game_over = True
                self.game.winner = current_player
                self.show_game_result()
            elif self.game.board.is_full():
                self.game.game_over = True
                self.show_game_result()
            else:
                # 切换玩家
                self.game.current_player_index = 1 - self.game.current_player_index
                self.update_status()
    
    def on_mouse_move(self, event):
        """鼠标移动事件 - 显示预览"""
        if not self.game or self.game.game_over:
            return
        
        current_player = self.game.players[self.game.current_player_index]
        if current_player.is_ai:
            return
        
        # 清除之前的预览
        self.canvas.delete("preview")
        
        # 计算鼠标位置对应的格子
        col = round((event.x - self.board_margin) / self.cell_size)
        row = round((event.y - self.board_margin) / self.cell_size)
        
        # 检查坐标是否有效且位置为空
        if (0 <= row < self.board_size and 0 <= col < self.board_size and 
            self.game.board.is_valid_move(row, col)):
            
            x = self.board_margin + col * self.cell_size
            y = self.board_margin + row * self.cell_size
            radius = self.cell_size // 2 - 2
            
            color = self.colors['black'] if current_player.symbol == 1 else self.colors['white']
            
            # 绘制半透明预览
            self.canvas.create_oval(
                x - radius, y - radius,
                x + radius, y + radius,
                fill=color, outline=color,
                stipple='gray50', tags="preview"
            )
        else:
            # 鼠标移出棋盘范围，清除预览
            self.canvas.delete("preview")
    
    def on_mouse_leave(self, event):
        """鼠标离开画布事件"""
        self.canvas.delete("preview")
    
    def start_game(self):
        """开始游戏"""
        self.game = Game()
        
        if self.mode_var.get() == "human":
            # 双人对战
            self.game.players = [
                Player("玩家1 (黑)", 1),
                Player("玩家2 (白)", 2)
            ]
        else:
            # 人机对战
            difficulty = self.difficulty_var.get()
            self.game.players = [
                Player("玩家 (黑)", 1),
                AIPlayer("电脑 (白)", 2, difficulty)
            ]
        
        self.game.current_player_index = 0
        self.game.game_over = False
        self.game.winner = None
        
        # 开始计时
        import time
        self.start_time = time.time()
        
        self.draw_board()
        self.update_status()
        self.update_move_count()
        self.update_time_display()
        
        # 启动时间更新定时器
        self.update_timer()
    
    def update_timer(self):
        """更新计时器"""
        if not self.game or not self.game.game_over:
            if hasattr(self, 'start_time'):
                self.update_time_display()
            # 每秒更新一次
            self.root.after(1000, self.update_timer)
    
    def restart_game(self):
        """重新开始游戏"""
        if self.game:
            self.start_game()
    
    def undo_move(self):
        """悔棋功能（简化版）"""
        messagebox.showinfo("提示", "悔棋功能暂未实现，请重新开始游戏")
    
    def quit_game(self):
        """退出游戏"""
        if messagebox.askokcancel("退出", "确定要退出游戏吗？"):
            self.root.quit()
    
    def update_status(self):
        """更新游戏状态显示"""
        if not self.game:
            return
        
        current_player = self.game.players[self.game.current_player_index]
        symbol = "●" if current_player.symbol == 1 else "○"
        
        if self.game.game_over:
            if self.game.winner:
                self.status_label.config(
                    text=f"🎉 {self.game.winner.name} 获胜！",
                    fg=self.colors['success']
                )
            else:
                self.status_label.config(
                    text="游戏平局！",
                    fg=self.colors['warning']
                )
        else:
            self.status_label.config(
                text=f"轮到 {current_player.name}",
                fg=self.colors['text_primary']
            )
        
        self.current_player_label.config(
            text=f"当前: {symbol} {current_player.name}"
        )
        
        # 更新玩家指示器
        if current_player.symbol == 1:
            self.player_indicator.config(bg='black', relief=tk.RAISED, bd=2)
        else:
            self.player_indicator.config(bg='white', relief=tk.SOLID, bd=2)
        
    def update_time_display(self):
        """更新时间显示"""
        if hasattr(self, 'start_time') and self.start_time:
            import time
            elapsed = int(time.time() - self.start_time)
            minutes = elapsed // 60
            seconds = elapsed % 60
            self.time_label.config(text=f"⏱️ 用时: {minutes:02d}:{seconds:02d}")
    
    def update_move_count(self):
        """更新步数统计"""
        if not self.game:
            return
        
        count = 0
        for row in self.game.board.board:
            count += sum(1 for cell in row if cell != 0)
        
        self.move_count_label.config(text=f"步数: {count}")
    
    def show_game_result(self):
        """显示游戏结果"""
        self.update_status()
        
        if self.game.winner:
            result = f"🎉 恭喜 {self.game.winner.name} 获胜！\n\n是否再来一局？"
        else:
            result = "游戏平局！\n\n是否再来一局？"
        
        if messagebox.askyesno("游戏结束", result):
            self.start_game()
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = GomokuGUI()
    app.run()