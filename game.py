from board import Board
from player import Player
from ai import AIPlayer

class Game:
    def __init__(self):
        self.board = Board()
        self.players = []
        self.current_player_index = 0
        self.game_over = False
        self.winner = None
    
    def setup_game(self, mode):
        """设置游戏模式"""
        if mode == 1:  # 双人对战
            self.players = [
                Player("玩家1", 1),
                Player("玩家2", 2)
            ]
        elif mode == 2:  # 人机对战
            difficulty = self._get_difficulty()
            self.players = [
                Player("玩家", 1),
                AIPlayer("电脑", 2, difficulty)
            ]
        else:
            raise ValueError("无效的游戏模式")
    
    def _get_difficulty(self):
        """获取AI难度"""
        while True:
            print("请选择AI难度:")
            print("1. 简单")
            print("2. 中等")
            print("3. 困难")
            
            try:
                choice = int(input("请输入选择 (1-3): "))
                if choice == 1:
                    return 'easy'
                elif choice == 2:
                    return 'medium'
                elif choice == 3:
                    return 'hard'
                else:
                    print("请输入1-3之间的数字")
            except ValueError:
                print("输入错误，请输入数字")
    
    def play(self):
        """开始游戏"""
        print("\n游戏开始！")
        print("黑子(●)先手，白子(○)后手")
        print("输入格式：行号 列号 (例如: 7 7)\n")
        
        while not self.game_over:
            current_player = self.players[self.current_player_index]
            
            # 显示棋盘
            self.board.display()
            
            # 获取落子位置
            if current_player.is_ai:
                print(f"{current_player.name} 正在思考...")
                row, col = current_player.get_move(self.board)
                print(f"{current_player.name} 落子于: ({row}, {col})")
            else:
                row, col = current_player.get_move(self.board)
            
            # 尝试落子
            if self.board.make_move(row, col, current_player.symbol):
                # 检查是否获胜
                if self.board.check_winner(row, col, current_player.symbol):
                    self.game_over = True
                    self.winner = current_player
                # 检查是否平局
                elif self.board.is_full():
                    self.game_over = True
                    self.winner = None
                else:
                    # 切换玩家
                    self.current_player_index = 1 - self.current_player_index
            else:
                print("无效的落子位置，请重新输入！")
        
        # 游戏结束
        self.board.display()
        if self.winner:
            print(f"🎉 恭喜 {self.winner.name} 获胜！")
        else:
            print("游戏平局！")
    
    def reset(self):
        """重置游戏"""
        self.board = Board()
        self.current_player_index = 0
        self.game_over = False
        self.winner = None