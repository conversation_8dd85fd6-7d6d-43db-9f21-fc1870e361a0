class Player:
    def __init__(self, name, symbol, is_ai=False):
        self.name = name
        self.symbol = symbol  # 1 for black, 2 for white
        self.is_ai = is_ai
    
    def get_move(self, board):
        """获取玩家的落子位置"""
        if self.is_ai:
            # AI玩家的移动将在AI类中实现
            raise NotImplementedError("AI move should be handled by AI class")
        else:
            # 人类玩家输入
            while True:
                try:
                    move_input = input(f"{self.name}，请输入落子位置 (行 列): ")
                    row, col = map(int, move_input.split())
                    return row, col
                except ValueError:
                    print("输入格式错误，请输入两个数字，用空格分隔")
                except:
                    print("输入错误，请重新输入")