<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}五子棋大师 - <PERSON><PERSON><PERSON> Master{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Socket.IO -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #ecf0f1;
            --dark-bg: #2c3e50;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --border-color: #bdc3c7;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--light-bg) 0%, #d5dbdb 100%);
            min-height: 100vh;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--accent-color) 0%, #2980b9 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9 0%, var(--accent-color) 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #229954 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e67e22 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid var(--border-color);
            padding: 12px 15px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .footer {
            background: var(--dark-bg);
            color: white;
            padding: 20px 0;
            margin-top: auto;
        }
        
        .loading {
            display: none;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner-border {
            width: 1.5rem;
            height: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .card {
                margin-bottom: 20px;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-chess-board me-2"></i>
                五子棋大师
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/game">
                            <i class="fas fa-gamepad me-1"></i>开始游戏
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="onlineCount">
                            <i class="fas fa-users me-1"></i>在线: <span id="onlineNumber">0</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container my-5">
        <!-- 消息提示 -->
        <div id="messageContainer"></div>
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- 页脚 -->
    <footer class="footer mt-auto">
        <div class="container text-center">
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-chess-board me-2"></i>五子棋大师</h6>
                    <p class="mb-0">智慧与策略的对决</p>
                </div>
                <div class="col-md-6">
                    <p class="mb-0">
                        <i class="fas fa-code me-1"></i>
                        由 AI Assistant 开发
                    </p>
                    <p class="mb-0">
                        <small>&copy; 2024 Gomoku Master. All rights reserved.</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 通用JavaScript -->
    <script>
        // 全局变量
        let socket = null;
        
        // 初始化Socket连接
        function initSocket() {
            socket = io();
            
            socket.on('connect', function() {
                console.log('已连接到服务器');
                updateConnectionStatus(true);
            });
            
            socket.on('disconnect', function() {
                console.log('与服务器断开连接');
                updateConnectionStatus(false);
            });
            
            socket.on('error', function(data) {
                showMessage('错误: ' + data.message, 'danger');
            });
        }
        
        // 更新连接状态
        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            if (statusElement) {
                statusElement.className = connected ? 'text-success' : 'text-danger';
                statusElement.innerHTML = connected ? 
                    '<i class="fas fa-wifi me-1"></i>已连接' : 
                    '<i class="fas fa-wifi me-1"></i>连接断开';
            }
        }
        
        // 显示消息
        function showMessage(message, type = 'info', duration = 5000) {
            const container = document.getElementById('messageContainer');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            container.appendChild(alertDiv);
            
            // 自动移除消息
            if (duration > 0) {
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, duration);
            }
        }
        
        // 显示加载状态
        function showLoading(show = true) {
            const loadingElements = document.querySelectorAll('.loading');
            loadingElements.forEach(element => {
                if (show) {
                    element.classList.add('show');
                } else {
                    element.classList.remove('show');
                }
            });
        }
        
        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showMessage('已复制到剪贴板', 'success', 2000);
            }).catch(() => {
                showMessage('复制失败', 'danger', 2000);
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSocket();
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>