{% extends "base.html" %}

{% block title %}五子棋大师 - 游戏{% endblock %}

{% block extra_css %}
<style>
    .game-container {
        max-width: 1400px;
        margin: 0 auto;
    }
    
    .game-board {
        background: linear-gradient(45deg, #f4d03f 0%, #f7dc6f 100%);
        border: 3px solid #8b4513;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        position: relative;
    }
    
    .board-grid {
        display: grid;
        grid-template-columns: repeat(15, 1fr);
        grid-template-rows: repeat(15, 1fr);
        gap: 1px;
        background: #8b4513;
        padding: 10px;
        border-radius: 5px;
        aspect-ratio: 1;
        max-width: 600px;
        margin: 0 auto;
    }
    
    .board-cell {
        background: linear-gradient(45deg, #f4d03f 0%, #f7dc6f 100%);
        border: none;
        cursor: pointer;
        position: relative;
        transition: background-color 0.2s ease;
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .board-cell:hover {
        background: rgba(255, 255, 255, 0.3);
    }
    
    .board-cell.disabled {
        cursor: not-allowed;
    }
    
    .stone {
        width: 80%;
        height: 80%;
        border-radius: 50%;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        transition: transform 0.3s ease;
    }
    
    .stone.black {
        background: radial-gradient(circle at 30% 30%, #555, #000);
        border: 1px solid #333;
    }
    
    .stone.white {
        background: radial-gradient(circle at 30% 30%, #fff, #ddd);
        border: 1px solid #ccc;
    }
    
    .stone.last-move {
        animation: pulse 1s ease-in-out;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .game-info {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .player-info {
        display: flex;
        align-items: center;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }
    
    .player-info.active {
        background: linear-gradient(135deg, var(--accent-color), #2980b9);
        color: white;
        transform: scale(1.02);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
    }
    
    .player-stone {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        margin-right: 15px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .game-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 20px;
    }
    
    .game-controls .btn {
        flex: 1;
        min-width: 120px;
    }
    
    .move-history {
        max-height: 300px;
        overflow-y: auto;
        background: #f8f9fa;
        border-radius: 5px;
        padding: 10px;
    }
    
    .move-item {
        padding: 5px 10px;
        border-radius: 3px;
        margin-bottom: 5px;
        background: white;
        border-left: 3px solid var(--accent-color);
    }
    
    .room-info {
        background: linear-gradient(135deg, var(--success-color), #229954);
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .connection-status {
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 1000;
        padding: 10px 15px;
        border-radius: 5px;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .game-result {
        text-align: center;
        padding: 30px;
        border-radius: 15px;
        margin: 20px 0;
    }
    
    .game-result.win {
        background: linear-gradient(135deg, var(--success-color), #229954);
        color: white;
    }
    
    .game-result.lose {
        background: linear-gradient(135deg, var(--danger-color), #c0392b);
        color: white;
    }
    
    .game-result.draw {
        background: linear-gradient(135deg, var(--warning-color), #e67e22);
        color: white;
    }
    
    @media (max-width: 768px) {
        .board-grid {
            max-width: 350px;
        }
        
        .game-controls .btn {
            min-width: 100px;
            font-size: 0.9rem;
        }
        
        .player-info {
            padding: 10px;
        }
        
        .player-stone {
            width: 25px;
            height: 25px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="game-container">
    <!-- 连接状态 -->
    <div class="connection-status" id="connectionStatus">
        <i class="fas fa-wifi me-1"></i>连接中...
    </div>
    
    <!-- 房间信息 -->
    <div class="room-info" id="roomInfo" style="display: none;">
        <h5 class="mb-2">
            <i class="fas fa-door-open me-2"></i>
            房间: <span id="roomId">-</span>
        </h5>
        <p class="mb-0">
            <i class="fas fa-share-alt me-1"></i>
            分享房间ID给朋友一起游戏
            <button class="btn btn-sm btn-outline-light ms-2" onclick="copyRoomId()">
                <i class="fas fa-copy"></i>
            </button>
        </p>
    </div>
    
    <div class="row">
        <!-- 左侧控制面板 -->
        <div class="col-lg-4 order-lg-1 order-2">
            <!-- 游戏模式选择 -->
            <div class="game-info" id="gameModeSelection">
                <h5 class="mb-3">
                    <i class="fas fa-gamepad me-2"></i>
                    选择游戏模式
                </h5>
                
                <div class="mb-3">
                    <label class="form-label">玩家昵称</label>
                    <input type="text" class="form-control" id="playerName" placeholder="输入你的昵称" maxlength="20">
                </div>
                
                <div class="mb-3">
                    <label class="form-label">游戏模式</label>
                    <select class="form-select" id="gameMode">
                        <option value="pvp">在线对战</option>
                        <option value="pve">人机对战</option>
                    </select>
                </div>
                
                <div class="mb-3" id="aiDifficultySection" style="display: none;">
                    <label class="form-label">AI难度</label>
                    <select class="form-select" id="aiDifficulty">
                        <option value="easy">简单</option>
                        <option value="medium">中等</option>
                        <option value="hard">困难</option>
                    </select>
                </div>
                
                <div class="mb-3" id="roomSection" style="display: none;">
                    <label class="form-label">房间操作</label>
                    <div class="d-flex gap-2">
                        <input type="text" class="form-control" id="joinRoomId" placeholder="输入房间ID">
                        <button class="btn btn-outline-primary" onclick="joinRoom()">
                            <i class="fas fa-sign-in-alt"></i>
                        </button>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-primary btn-lg" onclick="startGame()">
                        <i class="fas fa-play me-2"></i>
                        <span id="startButtonText">创建房间</span>
                    </button>
                </div>
            </div>
            
            <!-- 游戏信息 -->
            <div class="game-info" id="gameInfo" style="display: none;">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    游戏信息
                </h5>
                
                <!-- 玩家信息 -->
                <div id="playersInfo">
                    <div class="player-info" id="player1Info">
                        <div class="player-stone black"></div>
                        <div>
                            <strong id="player1Name">玩家1</strong>
                            <div class="small text-muted">黑子 - 先手</div>
                        </div>
                    </div>
                    
                    <div class="player-info" id="player2Info">
                        <div class="player-stone white"></div>
                        <div>
                            <strong id="player2Name">等待玩家...</strong>
                            <div class="small text-muted">白子 - 后手</div>
                        </div>
                    </div>
                </div>
                
                <!-- 游戏状态 -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>当前回合:</span>
                        <span id="currentTurn" class="fw-bold">等待开始</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>步数:</span>
                        <span id="moveCount">0</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>游戏状态:</span>
                        <span id="gameStatus" class="fw-bold">等待中</span>
                    </div>
                </div>
                
                <!-- 游戏控制 -->
                <div class="game-controls">
                    <button class="btn btn-warning" onclick="undoMove()" id="undoBtn" disabled>
                        <i class="fas fa-undo me-1"></i>悔棋
                    </button>
                    <button class="btn btn-success" onclick="restartGame()" id="restartBtn">
                        <i class="fas fa-redo me-1"></i>重开
                    </button>
                    <button class="btn btn-danger" onclick="leaveGame()" id="leaveBtn">
                        <i class="fas fa-sign-out-alt me-1"></i>离开
                    </button>
                </div>
            </div>
            
            <!-- 移动历史 -->
            <div class="game-info" id="moveHistoryPanel" style="display: none;">
                <h6 class="mb-3">
                    <i class="fas fa-history me-2"></i>
                    移动历史
                </h6>
                <div class="move-history" id="moveHistory">
                    <!-- 移动历史将在这里显示 -->
                </div>
            </div>
        </div>
        
        <!-- 中央游戏区域 -->
        <div class="col-lg-8 order-lg-2 order-1">
            <!-- 游戏结果 -->
            <div id="gameResult" style="display: none;"></div>
            
            <!-- 游戏棋盘 -->
            <div class="game-board">
                <div class="board-grid" id="gameBoard">
                    <!-- 棋盘格子将通过JavaScript生成 -->
                </div>
                
                <!-- 加载状态 -->
                <div class="loading text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在连接游戏...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 游戏状态变量
    let gameState = {
        roomId: null,
        playerId: null,
        playerName: '',
        gameMode: 'pvp',
        board: [],
        currentPlayer: null,
        gameStatus: 'waiting',
        players: [],
        moveHistory: [],
        isMyTurn: false
    };
    
    // 初始化游戏
    function initGame() {
        createBoard();
        setupEventListeners();
        
        // 从URL参数获取游戏模式
        const urlParams = new URLSearchParams(window.location.search);
        const mode = urlParams.get('mode');
        if (mode) {
            document.getElementById('gameMode').value = mode;
            updateGameModeUI();
        }
    }
    
    // 创建棋盘
    function createBoard() {
        const board = document.getElementById('gameBoard');
        board.innerHTML = '';
        
        for (let i = 0; i < 15; i++) {
            for (let j = 0; j < 15; j++) {
                const cell = document.createElement('button');
                cell.className = 'board-cell';
                cell.dataset.row = i;
                cell.dataset.col = j;
                cell.onclick = () => makeMove(i, j);
                board.appendChild(cell);
            }
        }
        
        // 初始化棋盘状态
        gameState.board = Array(15).fill().map(() => Array(15).fill(0));
    }
    
    // 设置事件监听器
    function setupEventListeners() {
        // 游戏模式变化
        document.getElementById('gameMode').addEventListener('change', updateGameModeUI);
        
        // Socket事件
        if (socket) {
            socket.on('player_joined', onPlayerJoined);
            socket.on('game_update', onGameUpdate);
            socket.on('game_over', onGameOver);
            socket.on('game_reset', onGameReset);
            socket.on('move_error', onMoveError);
        }
    }
    
    // 更新游戏模式UI
    function updateGameModeUI() {
        const mode = document.getElementById('gameMode').value;
        const aiSection = document.getElementById('aiDifficultySection');
        const roomSection = document.getElementById('roomSection');
        const startButton = document.getElementById('startButtonText');
        
        if (mode === 'pve') {
            aiSection.style.display = 'block';
            roomSection.style.display = 'none';
            startButton.textContent = '开始游戏';
        } else {
            aiSection.style.display = 'none';
            roomSection.style.display = 'block';
            startButton.textContent = '创建房间';
        }
        
        gameState.gameMode = mode;
    }
    
    // 开始游戏
    function startGame() {
        const playerName = document.getElementById('playerName').value.trim();
        if (!playerName) {
            showMessage('请输入玩家昵称', 'warning');
            return;
        }
        
        gameState.playerName = playerName;
        showLoading(true);
        
        const gameMode = document.getElementById('gameMode').value;
        const aiDifficulty = document.getElementById('aiDifficulty').value;
        
        // 创建房间
        fetch('/api/create_room', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                player_name: playerName,
                game_mode: gameMode,
                ai_difficulty: aiDifficulty
            })
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                gameState.roomId = data.room_id;
                gameState.playerId = data.player_id;
                
                // 加入Socket房间
                socket.emit('join_game_room', {
                    room_id: data.room_id,
                    player_id: data.player_id
                });
                
                // 更新UI
                showGameUI();
                updateRoomInfo(data.room_id);
                showMessage('房间创建成功！', 'success');
            } else {
                showMessage('创建房间失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showLoading(false);
            showMessage('网络错误: ' + error.message, 'danger');
        });
    }
    
    // 加入房间
    function joinRoom() {
        const roomId = document.getElementById('joinRoomId').value.trim();
        const playerName = document.getElementById('playerName').value.trim();
        
        if (!roomId) {
            showMessage('请输入房间ID', 'warning');
            return;
        }
        
        if (!playerName) {
            showMessage('请输入玩家昵称', 'warning');
            return;
        }
        
        showLoading(true);
        
        fetch('/api/join_room', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                room_id: roomId,
                player_name: playerName
            })
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                gameState.roomId = data.room_id;
                gameState.playerId = data.player_id;
                gameState.playerName = playerName;
                
                // 加入Socket房间
                socket.emit('join_game_room', {
                    room_id: data.room_id,
                    player_id: data.player_id
                });
                
                // 更新UI
                showGameUI();
                updateRoomInfo(data.room_id);
                showMessage('成功加入房间！', 'success');
            } else {
                showMessage('加入房间失败: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showLoading(false);
            showMessage('网络错误: ' + error.message, 'danger');
        });
    }
    
    // 显示游戏UI
    function showGameUI() {
        document.getElementById('gameModeSelection').style.display = 'none';
        document.getElementById('gameInfo').style.display = 'block';
        document.getElementById('moveHistoryPanel').style.display = 'block';
        showLoading(false);
    }
    
    // 更新房间信息
    function updateRoomInfo(roomId) {
        document.getElementById('roomId').textContent = roomId;
        document.getElementById('roomInfo').style.display = 'block';
    }
    
    // 复制房间ID
    function copyRoomId() {
        copyToClipboard(gameState.roomId);
    }
    
    // 落子
    function makeMove(row, col) {
        console.log('点击棋盘:', { row, col, isMyTurn: gameState.isMyTurn, gameStatus: gameState.gameStatus, currentPlayer: gameState.currentPlayer, playerName: gameState.playerName });
        
        if (!gameState.isMyTurn) {
            showMessage('还没轮到你下棋', 'warning', 2000);
            return;
        }
        
        if (gameState.gameStatus !== 'playing') {
            showMessage('游戏还未开始或已结束', 'warning', 2000);
            return;
        }
        
        if (gameState.board[row][col] !== 0) {
            showMessage('该位置已有棋子', 'warning', 2000);
            return;
        }
        
        // 发送落子请求
        socket.emit('make_move', {
            room_id: gameState.roomId,
            player_id: gameState.playerId,
            row: row,
            col: col
        });
    }
    
    // 悔棋
    function undoMove() {
        // 实现悔棋逻辑
        showMessage('悔棋功能开发中', 'info');
    }
    
    // 重新开始
    function restartGame() {
        if (confirm('确定要重新开始游戏吗？')) {
            socket.emit('restart_game', {
                room_id: gameState.roomId,
                player_id: gameState.playerId
            });
        }
    }
    
    // 离开游戏
    function leaveGame() {
        if (confirm('确定要离开游戏吗？')) {
            window.location.href = '/';
        }
    }
    
    // Socket事件处理
    function onPlayerJoined(data) {
        showMessage('新玩家加入游戏', 'info');
        updateGameStatus();
    }
    
    function onGameUpdate(data) {
        // 更新棋盘
        gameState.board = data.board;
        updateBoardDisplay();
        
        // 更新游戏状态
        gameState.currentPlayer = data.current_player;
        gameState.gameStatus = data.game_status;
        
        // 更新UI
        updateGameStatus();
        
        // 添加移动历史
        if (data.last_move) {
            addMoveToHistory(data.last_move);
            
            // 如果是AI移动，显示特殊提示
            if (data.last_move.is_ai) {
                showMessage('AI已落子', 'info', 2000);
                highlightLastMove(data.last_move.row, data.last_move.col);
            }
            
            // 播放落子音效
            if (typeof playMoveSound === 'function') {
                playMoveSound();
            }
        }
        
        // 更新轮次提示
        gameState.isMyTurn = (gameState.currentPlayer === gameState.playerName);
        
        // 在PVE模式下，如果当前玩家不是AI，就是轮到玩家了
        if (gameState.gameMode === 'pve' && gameState.currentPlayer !== 'AI') {
            gameState.isMyTurn = true;
        }
        
        console.log('游戏更新事件:', {
            currentPlayer: gameState.currentPlayer,
            playerName: gameState.playerName,
            gameMode: gameState.gameMode,
            isMyTurn: gameState.isMyTurn,
            gameStatus: gameState.gameStatus
        });
        
        updateTurnIndicator();
    }
    
    function onGameOver(data) {
        gameState.gameStatus = 'finished';
        showGameResult(data.winner, data.reason);
        updateGameStatus();
    }
    
    function onGameReset(data) {
        gameState.board = data.board;
        gameState.currentPlayer = data.current_player;
        gameState.gameStatus = data.game_status;
        gameState.moveHistory = [];
        
        updateBoardDisplay();
        updateGameStatus();
        clearMoveHistory();
        hideGameResult();
        
        showMessage('游戏已重新开始', 'success');
    }
    
    function onMoveError(data) {
        showMessage(data.message, 'danger');
    }
    
    // 更新棋盘显示
    function updateBoardDisplay() {
        const cells = document.querySelectorAll('.board-cell');
        cells.forEach(cell => {
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);
            const value = gameState.board[row][col];
            
            cell.innerHTML = '';
            cell.classList.remove('disabled');
            
            if (value !== 0) {
                const stone = document.createElement('div');
                stone.className = `stone ${value === 1 ? 'black' : 'white'}`;
                cell.appendChild(stone);
                cell.classList.add('disabled');
            }
        });
        
        // 如果不是玩家回合，禁用所有空格
        if (!gameState.isMyTurn || gameState.gameStatus !== 'playing') {
            cells.forEach(cell => {
                if (!cell.querySelector('.stone')) {
                    cell.classList.add('disabled');
                }
            });
        }
    }
    
    // 高亮最后一步移动
    function highlightLastMove(row, col) {
        // 移除之前的高亮
        document.querySelectorAll('.stone.last-move').forEach(stone => {
            stone.classList.remove('last-move');
        });
        
        // 添加新的高亮
        const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
        if (cell) {
            const stone = cell.querySelector('.stone');
            if (stone) {
                stone.classList.add('last-move');
            }
        }
    }
    
    // 更新轮次指示器
    function updateTurnIndicator() {
        const turnIndicator = document.getElementById('currentTurn');
        if (turnIndicator) {
            if (gameState.isMyTurn) {
                turnIndicator.innerHTML = '<i class="fas fa-hand-point-right text-success me-2"></i>轮到你了';
                turnIndicator.className = 'badge bg-success fs-6';
            } else {
                turnIndicator.innerHTML = '<i class="fas fa-clock text-warning me-2"></i>等待对手';
                turnIndicator.className = 'badge bg-warning fs-6';
            }
        }
    }
    
    // 更新游戏状态
    function updateGameStatus() {
        // 获取房间状态
        if (gameState.roomId) {
            fetch(`/api/room/${gameState.roomId}/status`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const room = data.room;
                        const gameInfo = room.game_info;
                        
                        // 更新玩家信息
                        if (gameInfo && gameInfo.players) {
                            updatePlayersInfo(gameInfo.players);
                        }
                        
                        // 更新当前回合
                        document.getElementById('currentTurn').textContent = gameInfo?.current_player || '等待开始';
                        document.getElementById('moveCount').textContent = gameInfo?.move_count || 0;
                        document.getElementById('gameStatus').textContent = getGameStatusText(gameInfo?.game_status);
                        
                        // 更新是否轮到我
                        gameState.isMyTurn = gameInfo?.current_player === gameState.playerName;
                        
                        // 在PVE模式下，如果当前玩家不是AI，就是轮到玩家了
                        if (gameState.gameMode === 'pve' && gameInfo?.current_player !== 'AI') {
                            gameState.isMyTurn = true;
                        }
                        
                        console.log('游戏状态更新:', { 
                            currentPlayer: gameInfo?.current_player, 
                            playerName: gameState.playerName, 
                            gameMode: gameState.gameMode,
                            isMyTurn: gameState.isMyTurn,
                            gameStatus: gameInfo?.game_status
                        });
                        
                        updatePlayerTurnUI();
                        updateTurnIndicator();
                    }
                })
                .catch(error => {
                    console.error('获取房间状态失败:', error);
                });
        }
    }
    
    // 更新玩家信息
    function updatePlayersInfo(players) {
        const player1Name = document.getElementById('player1Name');
        const player2Name = document.getElementById('player2Name');
        
        if (players.length >= 1) {
            player1Name.textContent = players[0].name;
        }
        
        if (players.length >= 2) {
            player2Name.textContent = players[1].name;
        } else {
            player2Name.textContent = '等待玩家...';
        }
    }
    
    // 更新玩家回合UI
    function updatePlayerTurnUI() {
        const player1Info = document.getElementById('player1Info');
        const player2Info = document.getElementById('player2Info');
        
        player1Info.classList.remove('active');
        player2Info.classList.remove('active');
        
        if (gameState.currentPlayer === document.getElementById('player1Name').textContent) {
            player1Info.classList.add('active');
        } else if (gameState.currentPlayer === document.getElementById('player2Name').textContent) {
            player2Info.classList.add('active');
        }
    }
    
    // 获取游戏状态文本
    function getGameStatusText(status) {
        const statusMap = {
            'waiting': '等待中',
            'playing': '游戏中',
            'finished': '已结束',
            'paused': '已暂停'
        };
        return statusMap[status] || status;
    }
    
    // 添加移动到历史
    function addMoveToHistory(move) {
        const history = document.getElementById('moveHistory');
        const moveItem = document.createElement('div');
        moveItem.className = 'move-item';
        moveItem.innerHTML = `
            <strong>${move.player}</strong> 
            <span class="text-muted">第${move.move_number}步</span>
            <br>
            <small>位置: (${move.row + 1}, ${move.col + 1})</small>
        `;
        history.appendChild(moveItem);
        history.scrollTop = history.scrollHeight;
    }
    
    // 清空移动历史
    function clearMoveHistory() {
        document.getElementById('moveHistory').innerHTML = '';
    }
    
    // 显示游戏结果
    function showGameResult(winner, reason) {
        const resultDiv = document.getElementById('gameResult');
        let resultClass = 'draw';
        let resultText = '游戏结束';
        let resultIcon = 'fas fa-handshake';
        
        if (winner) {
            if (winner === gameState.playerName) {
                resultClass = 'win';
                resultText = '恭喜你获胜！';
                resultIcon = 'fas fa-trophy';
            } else {
                resultClass = 'lose';
                resultText = '很遗憾，你败了';
                resultIcon = 'fas fa-sad-tear';
            }
        } else {
            resultText = '平局！';
        }
        
        resultDiv.className = `game-result ${resultClass}`;
        resultDiv.innerHTML = `
            <i class="${resultIcon} fa-3x mb-3"></i>
            <h3>${resultText}</h3>
            <p>${reason || ''}</p>
            <button class="btn btn-light btn-lg" onclick="restartGame()">
                <i class="fas fa-redo me-2"></i>再来一局
            </button>
        `;
        resultDiv.style.display = 'block';
    }
    
    // 隐藏游戏结果
    function hideGameResult() {
        document.getElementById('gameResult').style.display = 'none';
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initGame();
        
        // 定期更新游戏状态
        setInterval(updateGameStatus, 5000);
    });
</script>
{% endblock %}