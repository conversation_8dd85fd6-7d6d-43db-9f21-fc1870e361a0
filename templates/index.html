{% extends "base.html" %}

{% block title %}五子棋大师 - 首页{% endblock %}

{% block content %}
<!-- 欢迎横幅 -->
<div class="row mb-5">
    <div class="col-12">
        <div class="card bg-primary text-white">
            <div class="card-body text-center py-5">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-chess-board me-3"></i>
                    五子棋大师
                </h1>
                <p class="lead mb-4">体验经典五子棋游戏的魅力，支持在线对战和人机挑战</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="/game" class="btn btn-light btn-lg">
                        <i class="fas fa-play me-2"></i>开始游戏
                    </a>
                    <button class="btn btn-outline-light btn-lg" onclick="showRules()">
                        <i class="fas fa-question-circle me-2"></i>游戏规则
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 游戏模式选择 -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">
            <i class="fas fa-gamepad me-2"></i>
            选择游戏模式
        </h2>
    </div>
    
    <!-- 在线对战 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-users fa-3x text-primary"></i>
                </div>
                <h4 class="card-title">在线对战</h4>
                <p class="card-text">与其他玩家实时对战，体验真正的竞技乐趣</p>
                <ul class="list-unstyled mb-4">
                    <li><i class="fas fa-check text-success me-2"></i>实时对战</li>
                    <li><i class="fas fa-check text-success me-2"></i>创建/加入房间</li>
                    <li><i class="fas fa-check text-success me-2"></i>观战模式</li>
                    <li><i class="fas fa-check text-success me-2"></i>聊天功能</li>
                </ul>
                <button class="btn btn-primary btn-lg" onclick="startOnlineGame()">
                    <i class="fas fa-wifi me-2"></i>开始在线对战
                </button>
            </div>
        </div>
    </div>
    
    <!-- 人机对战 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-robot fa-3x text-success"></i>
                </div>
                <h4 class="card-title">人机对战</h4>
                <p class="card-text">挑战智能AI，提升你的五子棋技巧</p>
                <ul class="list-unstyled mb-4">
                    <li><i class="fas fa-check text-success me-2"></i>多种难度</li>
                    <li><i class="fas fa-check text-success me-2"></i>智能AI</li>
                    <li><i class="fas fa-check text-success me-2"></i>单人练习</li>
                    <li><i class="fas fa-check text-success me-2"></i>技巧提升</li>
                </ul>
                <button class="btn btn-success btn-lg" onclick="startAIGame()">
                    <i class="fas fa-brain me-2"></i>挑战AI
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 游戏统计 -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">
            <i class="fas fa-chart-bar me-2"></i>
            实时统计
        </h2>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h4 class="card-title" id="onlinePlayersCount">0</h4>
                <p class="card-text">在线玩家</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-door-open fa-2x text-success mb-2"></i>
                <h4 class="card-title" id="activeRoomsCount">0</h4>
                <p class="card-text">活跃房间</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-gamepad fa-2x text-warning mb-2"></i>
                <h4 class="card-title" id="gamesPlayedCount">0</h4>
                <p class="card-text">今日对局</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-trophy fa-2x text-danger mb-2"></i>
                <h4 class="card-title" id="topPlayerName">-</h4>
                <p class="card-text">今日冠军</p>
            </div>
        </div>
    </div>
</div>

<!-- 游戏特色 -->
<div class="row mb-5">
    <div class="col-12">
        <h2 class="text-center mb-4">
            <i class="fas fa-star me-2"></i>
            游戏特色
        </h2>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                <h5 class="card-title">响应式设计</h5>
                <p class="card-text">完美适配手机、平板和电脑，随时随地畅玩</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-bolt fa-3x text-warning mb-3"></i>
                <h5 class="card-title">实时对战</h5>
                <p class="card-text">基于WebSocket的实时通信，零延迟游戏体验</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-brain fa-3x text-success mb-3"></i>
                <h5 class="card-title">智能AI</h5>
                <p class="card-text">多级难度AI对手，从新手到大师级别挑战</p>
            </div>
        </div>
    </div>
</div>

<!-- 游戏规则模态框 -->
<div class="modal fade" id="rulesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-book me-2"></i>
                    五子棋游戏规则
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-target me-2"></i>游戏目标</h6>
                        <p>在15×15的棋盘上，率先在横、竖、斜任意方向连成5子的一方获胜。</p>
                        
                        <h6><i class="fas fa-play me-2"></i>游戏流程</h6>
                        <ol>
                            <li>黑子先行，白子后行</li>
                            <li>轮流在棋盘交叉点落子</li>
                            <li>不能在已有棋子的位置落子</li>
                            <li>率先连成五子者获胜</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-trophy me-2"></i>获胜条件</h6>
                        <ul>
                            <li>横向连成5子</li>
                            <li>竖向连成5子</li>
                            <li>斜向连成5子</li>
                            <li>对手认输</li>
                        </ul>
                        
                        <h6><i class="fas fa-gamepad me-2"></i>操作说明</h6>
                        <ul>
                            <li>点击棋盘交叉点落子</li>
                            <li>支持悔棋功能（对方同意）</li>
                            <li>可以重新开始游戏</li>
                            <li>支持观战模式</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <a href="/game" class="btn btn-primary">开始游戏</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 显示游戏规则
    function showRules() {
        const modal = new bootstrap.Modal(document.getElementById('rulesModal'));
        modal.show();
    }
    
    // 开始在线对战
    function startOnlineGame() {
        window.location.href = '/game?mode=pvp';
    }
    
    // 开始AI对战
    function startAIGame() {
        window.location.href = '/game?mode=pve';
    }
    
    // 更新统计数据
    function updateStats() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('onlinePlayersCount').textContent = data.stats.online_players || 0;
                    document.getElementById('activeRoomsCount').textContent = data.stats.active_rooms || 0;
                    document.getElementById('gamesPlayedCount').textContent = data.stats.games_today || 0;
                    document.getElementById('topPlayerName').textContent = data.stats.top_player || '-';
                }
            })
            .catch(error => {
                console.error('获取统计数据失败:', error);
            });
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 定期更新统计数据
        updateStats();
        setInterval(updateStats, 30000); // 每30秒更新一次
        
        // Socket事件监听
        if (socket) {
            socket.on('stats_update', function(data) {
                document.getElementById('onlinePlayersCount').textContent = data.online_players || 0;
                document.getElementById('activeRoomsCount').textContent = data.active_rooms || 0;
            });
        }
    });
</script>
{% endblock %}