#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五子棋游戏主程序
支持命令行、GUI和Web三种界面
"""

import sys
import argparse
from game import Game

def main():
    parser = argparse.ArgumentParser(description='五子棋游戏')
    parser.add_argument('--mode', choices=['cli', 'gui', 'web'], default='gui',
                       help='选择游戏界面模式 (默认: gui)')
    parser.add_argument('--player1', choices=['human', 'ai'], default='human',
                       help='玩家1类型 (默认: human)')
    parser.add_argument('--player2', choices=['human', 'ai'], default='human',
                       help='玩家2类型 (默认: human)')
    parser.add_argument('--ai-level', choices=['easy', 'medium', 'hard'], default='medium',
                       help='AI难度级别 (默认: medium)')
    parser.add_argument('--host', default='127.0.0.1',
                       help='Web服务器主机地址 (默认: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=5000,
                       help='Web服务器端口 (默认: 5000)')
    parser.add_argument('--debug', action='store_true',
                       help='启用调试模式')
    
    args = parser.parse_args()
    
    try:
        if args.mode == 'web':
            # 启动Web界面
            from app import app, socketio
            print(f"启动五子棋Web服务器...")
            print(f"访问地址: http://{args.host}:{args.port}")
            socketio.run(app, host=args.host, port=args.port, debug=args.debug)
        elif args.mode == 'gui':
            # 启动GUI界面
            from main_gui import main as gui_main
            gui_main()
        else:
            # 启动命令行界面
            game = Game()
            game.setup_players(args.player1, args.player2, args.ai_level)
            game.play()
            
    except KeyboardInterrupt:
        print("\n游戏被用户中断")
        sys.exit(0)
    except ImportError as e:
        if 'tkinter' in str(e) and args.mode == 'gui':
            print("错误: 无法启动GUI界面，tkinter模块未找到")
            print("请尝试使用命令行模式: python main.py --mode cli")
            print("或者使用Web模式: python main.py --mode web")
        elif 'Flask' in str(e) and args.mode == 'web':
            print("错误: 无法启动Web界面，Flask模块未找到")
            print("请安装依赖: uv sync")
            print("或者使用命令行模式: python main.py --mode cli")
        else:
            print(f"导入错误: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"游戏运行出错: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()